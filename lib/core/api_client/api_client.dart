import 'dart:async';
// import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flowkar/core/errors/exceptions.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/analytics/model/get_analytics_platforms_model.dart';
import 'package:flowkar/features/analytics/presentation/page/facebook_analytics/model/facebook_impressions_follower_model.dart';
import 'package:flowkar/features/analytics/presentation/page/facebook_analytics/model/facebook_post_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/instagram_analytics/model/instagram_line_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/instagram_analytics/model/instagram_pie_chart_model.dart';
import 'package:flowkar/features/analytics/presentation/page/linkedin_analytics/model/follower_impretion_posts_model.dart';
import 'package:flowkar/features/analytics/presentation/page/linkedin_analytics/model/linkedin_engagement_share_model.dart';
import 'package:flowkar/features/analytics/presentation/page/linkedin_analytics/model/linkedin_pie_chart_model.dart';
import 'package:flowkar/features/analytics/presentation/page/pinterest/model/pinterest_line_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/pinterest/model/pintrest_total_count_model.dart';
import 'package:flowkar/features/analytics/presentation/page/thread_analytics/model/thread_country_model.dart';
import 'package:flowkar/features/analytics/presentation/page/thread_analytics/model/thread_line_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/youtube_analytics/model/youtube_subscribe_like_comment_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/youtube_analytics/model/youtube_video_graph_model.dart';
import 'package:flowkar/features/authentication/model/delete_account_response.dart';
import 'package:flowkar/features/authentication/model/edit_brand_by_id_model.dart';
import 'package:flowkar/features/authentication/model/forgot_password_response_model.dart';
import 'package:flowkar/features/authentication/model/get_brands_model.dart';
import 'package:flowkar/features/authentication/model/get_curent_brand_id_model.dart';
import 'package:flowkar/features/authentication/model/get_user_and_industry_type.dart';
import 'package:flowkar/features/authentication/model/get_user_status.dart';
import 'package:flowkar/features/authentication/model/login_response_model.dart';
import 'package:flowkar/features/authentication/model/registaer_brand_model.dart';
import 'package:flowkar/features/authentication/model/register_response_model.dart';
import 'package:flowkar/features/authentication/model/reset_password_response_model.dart';
import 'package:flowkar/features/authentication/model/select_user_and_industry_type.dart';
import 'package:flowkar/features/authentication/model/switch_user_account_model.dart';
import 'package:flowkar/features/authentication/model/permissions_model.dart';
import 'package:flowkar/features/authentication/model/verify_otp_response_model.dart';
// import 'package:flowkar/features/chat/model/chat_delete_model.dart';
import 'package:flowkar/features/discover/model/hashtag_post_model.dart';

import 'package:flowkar/features/home_feed_screen/model/analytics_model.dart';
import 'package:flowkar/features/home_feed_screen/model/blocked_users_model.dart';
import 'package:flowkar/features/home_feed_screen/model/delete_comment_model.dart';
import 'package:flowkar/features/home_feed_screen/model/delete_draft_post_model.dart';
import 'package:flowkar/features/home_feed_screen/model/delete_post_model.dart';
import 'package:flowkar/features/home_feed_screen/model/edit_post_model.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/model/panding_aprovel_post_model.dart';
import 'package:flowkar/features/sm_chat/model/ai_generate_massage_or_comment_model.dart';
import 'package:flowkar/features/upload_post/model/generate_duplicate_post_model.dart';
import 'package:flowkar/features/home_feed_screen/model/get_comment_model.dart';
import 'package:flowkar/features/reward_leader/model/get_reward_data_model.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:flowkar/features/home_feed_screen/model/post_share_model.dart';
import 'package:flowkar/features/home_feed_screen/model/upload_draft_post_model.dart';
import 'package:flowkar/features/home_feed_screen/model/user_block_model.dart';
import 'package:flowkar/features/home_feed_screen/model/video_response_model.dart';
import 'package:flowkar/features/join_beta_tester/model/beta_user_signup_model.dart';
import 'package:flowkar/features/live_stream/model/live_user_model.dart';
import 'package:flowkar/features/notification/model/get_post_by_id_model.dart';
import 'package:flowkar/features/profile_screen/model/demography_response_model.dart';
import 'package:flowkar/features/profile_screen/model/edit_profile_response.dart';
import 'package:flowkar/features/profile_screen/model/get_follower_list_model.dart';
import 'package:flowkar/features/profile_screen/model/get_user_text_post_model.dart';
import 'package:flowkar/features/profile_screen/model/qr_generate_model.dart';
import 'package:flowkar/features/profile_screen/model/tag_post_list_model.dart';
import 'package:flowkar/features/profile_screen/model/user_profile_detail_model.dart';
import 'package:flowkar/features/notification/model/notification_list_model.dart';
import 'package:flowkar/features/profile_screen/model/scheduled_post_model.dart';
import 'package:flowkar/features/home_feed_screen/model/draft_post_model.dart';
import 'package:flowkar/features/reward_leader/model/leaderboard_model.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/model/planner_list_post_model.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/model/scheduled_posts_web_model.dart';
import 'package:flowkar/features/setting_screen/page/save_post/model/save_post_model.dart';
import 'package:flowkar/features/sm_chat/model/flowkar_chat_model/chat_list_model.dart';
import 'package:flowkar/features/sm_chat/model/flowkar_chat_model/chat_message_list_model.dart';
import 'package:flowkar/features/sm_chat/model/send_insta_msg_model.dart';
import 'package:flowkar/features/sm_chat/model/single_chat_model.dart';
import 'package:flowkar/features/sm_chat/model/sm_chat_list_model.dart';
import 'package:flowkar/features/sm_chat/model/telegram_send_masage_model.dart';
import 'package:flowkar/features/sm_chat/model/telegram_user_chat_list_model.dart';
import 'package:flowkar/features/sm_chat/model/telegram_user_list_model.dart';
import 'package:flowkar/features/social_connect/model/dynamic_platform_loading_model.dart';
import 'package:flowkar/features/social_connect/model/purchase_x_model.dart';
import 'package:flowkar/features/social_connect/model/share_profile_model.dart';
import 'package:flowkar/features/social_connect/model/social_connect_check.dart';
import 'package:flowkar/features/social_connect/model/social_disconect_model.dart';
import 'package:flowkar/features/social_connect/model/telegram_send_code_model.dart';
import 'package:flowkar/features/social_connect/model/telegram_signin_model.dart';
import 'package:flowkar/features/social_connect/model/thread_profile_model.dart';
import 'package:flowkar/features/social_connect/model/thumblr_social_media_model.dart';
import 'package:flowkar/features/social_connect/model/social_connect_model.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/models/story_model.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/model/delete_story_model.dart';
// import 'package:flowkar/features/story_module/model/get_all_story_model.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/model/upload_story_model.dart';
import 'package:flowkar/features/survey_form/model/survey_form_model.dart';
import 'package:flowkar/features/survey_form/model/update_survey_model.dart';
import 'package:flowkar/features/survey_form/model/user_home_data_model.dart';
import 'package:flowkar/features/update_version/model/version_update_model.dart';
import 'package:flowkar/features/upload_post/model/ai_generate_content_model.dart';
import 'package:flowkar/features/upload_post/model/post_industry_model.dart';
import 'package:flowkar/features/upload_post/model/search_location_model.dart';
import 'package:flowkar/features/upload_post/model/upload_post_response_model.dart';
import 'package:flowkar/features/upload_post/model/user_profile_model.dart';
import 'package:flowkar/features/user_management/model/get_invited_user_model.dart';
import 'package:flowkar/features/user_management/model/get_invitee_user_model.dart';
import 'package:flowkar/features/user_management/model/get_user_roles_model.dart';
import 'package:flowkar/features/wallet/model/wallet_model.dart';
import 'package:retrofit/retrofit.dart';

part 'api_client.g.dart';

final config = FlavorConfig.instance.env;

@RestApi(parser: Parser.FlutterCompute)
abstract class ApiClient {
  static bool _isNetworkErrorToastShowing = false;
  static bool _isstatus403 = false;
  static DateTime? _lastNetworkErrorTime;
  static DateTime? _lastStatus403ErrorTime;
  static const Duration _toastCooldownDuration = Duration(seconds: 5);
  static const Duration _toaststatus403Duration = Duration(seconds: 5);

  factory ApiClient(Dio dio, {String? baseUrl}) {
    final url = baseUrl ?? config.baseUrl;

    dio.options = BaseOptions(
        baseUrl: url,
        // connectTimeout: const Duration(seconds: 8),
        // receiveTimeout: const Duration(seconds: 5),
        headers: _buildHeaders());

    dio.interceptors.addAll([
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseHeader: true,
        responseBody: true,
        error: true,
        compact: true,
        maxWidth: 90,
      ),
      InterceptorsWrapper(
        onRequest: (RequestOptions options, RequestInterceptorHandler handler) async {
          final tmpAuthToken = Prefobj.preferences?.get(Prefkeys.TMPAUTHTOKEN);
          final authToken = Prefobj.preferences?.get(Prefkeys.AUTHTOKEN);
          if (tmpAuthToken != null && tmpAuthToken.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer $tmpAuthToken';
          } else if (authToken != null && authToken.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer $authToken';
          }
          handler.next(options);
        },
        onResponse: (Response response, ResponseInterceptorHandler handler) {
          handler.next(response);
        },
        onError: (DioException error, ErrorInterceptorHandler handler) async {
          if (error.message != null && error.message!.contains("The connection errored:")) {
            _showNetworkErrorToast();
          }

          // Handle 403 Forbidden - redirect to login
          if (error.response?.statusCode == 403) {
            await _handle403Error();
            handler.next(error);
            return;
          }

          final context = error.requestOptions.extra['context'] as BuildContext?;
          if (context != null) {
            final userFriendlyError = error.type.toUserFriendlyError(context);
            log(userFriendlyError.description);
          }
          handler.next(error);
        },
      ),
    ]);

    return _ApiClient(dio, baseUrl: url);
  }

  static void _showNetworkErrorToast() {
    final now = DateTime.now();

    // Check if toast is already showing or if we're within cooldown period
    if (_isNetworkErrorToastShowing) {
      return;
    }

    if (_lastNetworkErrorTime != null && now.difference(_lastNetworkErrorTime!) < _toastCooldownDuration) {
      return;
    }

    _isNetworkErrorToastShowing = true;
    _lastNetworkErrorTime = now;

    toastification.show(
      type: ToastificationType.error,
      showProgressBar: false,
      title: Text(
        "Network error, please check your internet connection.",
        maxLines: 2,
        style: GoogleFonts.montserrat(
          fontSize: 12.0.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
      autoCloseDuration: const Duration(seconds: 3),
      callbacks: ToastificationCallbacks(
        onAutoCompleteCompleted: (value) {
          _isNetworkErrorToastShowing = false;
        },
        onDismissed: (value) {
          _isNetworkErrorToastShowing = false;
        },
        onCloseButtonTap: (value) {
          _isNetworkErrorToastShowing = false;
        },
      ),
    );
  }

  static Future<void> _handle403Error() async {
    try {
      final now = DateTime.now();

      // Check if toast is already showing or if we're within cooldown period
      // if (_isstatus403) {
      //   return;
      // }

      if (_lastStatus403ErrorTime != null && now.difference(_lastStatus403ErrorTime!) < _toaststatus403Duration) {
        return;
      }

      _isstatus403 = true;
      _lastStatus403ErrorTime = now;
      log("403 Forbidden error detected - redirecting to login");

      // Clear all user data and authentication tokens
      await DataCleanupService.clearAllData();

      // Show error message to user
      toastification.show(
        type: ToastificationType.error,
        showProgressBar: false,
        title: Text(
          "Session expired. Please login again.",
          maxLines: 2,
          style: GoogleFonts.montserrat(
            fontSize: 12.0.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );
      Prefobj.preferences?.put(Prefkeys.FIRSTTIME, 'true');

      // Navigate to login screen and clear navigation stack
      NavigatorService.pushAndRemoveUntil(AppRoutes.signinPage);
    } catch (error) {
      log("Error handling 403 response: $error");
    }
  }

  static Map<String, String> _buildHeaders() {
    return <String, String>{
      'Content-Type': 'application/json',
      // 'user': Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? "",
      // 'brand': Prefobj.preferences?.get(Prefkeys.BRANDID).toString() ?? "",
    };
  }

// API methods
  @POST('register/')
  Future<RegisterResponseModel> register({
    @Field("name") String? name,
    @Field("username") String? username,
    @Field("email") String? email,
    @Field("password") String? password,
    @Field("phone") String? phone,
    @Field("refference_code") String? refferenceCode,
  });

  @POST('login/')
  Future<LoginResponseModel> login(
    @Field("creds") String email,
    @Field("password") String password,
  );
  @POST('login-switch/')
  Future<LoginResponseModel> addswithaccountlogin(
    @Field("creds") String email,
    @Field("password") String password,
  );

  @POST('forgot-password/')
  Future<ForgotPasswordResponseModel> forgotPassword({
    @Field("email") String? email,
  });

  @POST("verify-otp/")
  @FormUrlEncoded()
  Future<VerifyOtpResponseModel> verifyOtp({
    @Field("email") String? email,
    @Field("user_otp") String? userOtp,
  });

  @POST('reset-password/')
  Future<ResetPasswordResponseModel> resetPassword({@Field("new_password") String? newPassword});

  @GET('profile/')
  Future<UserProfileModel> getProfilePost({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });

  @POST("upload-post/")
  @MultiPart()
  Future<UploadPostResponseModel> uploadPost({
    @Header("user") String? logInUserId,
    @Header("subscription") String? subscriptionId,
    @Header("brand") String? brandid,
    @Part(name: "title") String? title,
    @Part(name: "description") String? description,
    @Part(name: "location") String? location,
    @Part(name: "is_private") bool? isPrivate,
    @Part(name: "is_video") bool? isVideo,
    @Part(name: "facebook") bool? facebook,
    @Part(name: "linkedin") bool? linkedin,
    @Part(name: "pinterest") bool? pinterest,
    @Part(name: "instagram") bool? instagram,
    @Part(name: "dailymotion") bool? dailymotion,
    @Part(name: "twitter") bool? twitter,
    @Part(name: "vimeo") bool? vimeo,
    @Part(name: "youtube") bool? youtube,
    @Part(name: "tumblr") bool? tumblr,
    @Part(name: "reddit") bool? reddit,
    @Part(name: "scheduled_at") String? scheduledAt,
    @Part(name: "tagged_in") String? taggedIn,
    @Part(name: "hashtags") String? hashtags,
    @Part(name: "upload_files") List<File>? uploadFiles,
    @Part(name: "thumbnail_files") List<File>? videoThumbnail,
    @Part(name: "is_posted") bool? isposted,
    @Part(name: "industry") int? industry,
    @Part(name: "is_text_post") bool? isTextPost,
    @Part(name: "x") bool? x,
    @Part(name: "mastadon") bool? mastadon,
  });

// Linkdin API
  @GET('linkedin-url/')
  Future<SocialConnectModel> connectLinkedin(
    @Header("brand") String brandid,
    @Header("user") String? logInUserId,
  );
  @GET('disconnect-linkedin/')
  Future<SocialDisconnectModel> disconnectLinkedin(
    @Header("brand") String brandid,
    @Header("user") String? logInUserId,
  );
// Vimeo API
  @GET('vimeo-url/')
  Future<SocialConnectModel> connectVimeo(
    @Header("brand") String brandid,
    @Header("user") String? logInUserId,
  );
  @GET('disconnect-vimeo/')
  Future<SocialDisconnectModel> disconnectVimeo(
    @Header("brand") String brandid,
    @Header("user") String? logInUserId,
  );
  @GET('delete-profile/')
  Future<DeleteAccountResponse> deleteAccount({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });

  @GET('get-thirdparty/')
  Future<SocialConnectCheck> socialConnectCheck(
    @Header("brand") String brandid,
    @Header("user") String? logInUserId,
  );
// Pintrest API
  @GET('pinterest-url/')
  Future<SocialConnectModel> connectPintrest(
    @Header("brand") String brandid,
    @Header("user") String? logInUserId,
  );
  @GET('disconnect-pinterest/')
  Future<SocialDisconnectModel> disconnectPintrest(
    @Header("brand") String brandid,
    @Header("user") String? logInUserId,
  );
// Reddit API
  @GET('reddit-auth/')
  Future<SocialConnectModel> connectReddit(
    @Header("brand") String brandid,
    @Header("user") String? logInUserId,
  );
  @GET('disconnect-reddit/')
  Future<SocialDisconnectModel> disconnectReddit(
    @Header("user") String? logInUserId,
    @Header("brand") String brandid,
  );
// Tumblr API
  @POST('tumblr-after-auth/')
  Future<SocialConnectModel> tumblrAfterAuthApi({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
    @Field('oauth_token') String? oauthToken,
    @Field('oauth_token_secret') String? oauthTokenSecret,
    @Field('oauth_verifier') String? oauthVerifier,
  });
  @GET('tumblr/')
  Future<ThumblrSocialMediaModel> connectTumblr({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
  @GET('disconnect-tumblr/')
  Future<SocialDisconnectModel> disconnectTumblr({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
// Get All Post
  @GET('get-all-post/?')
  Future<PostResponseModel> getAllPost({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
    @Query("page") int? page,
    @Query("request_type") String? type,
  });

// Get All Video
  @GET('get-all-post/?')
  Future<VideoResponseModel> getAllVideo(@Header("user") String? logInUserId, @Header("brand") String? brandid,
      @Query("page") int page, @Query("request_type") String type);
//Comment Post API
  @GET('comment/{id}')
  Future<GetCommentModel> getCommentById(
    @Header("user") String? logInUserId,
    @Path("id") String id,
    @Header("brand") String? brandid,
  );
  @DELETE('comment/{id}')
  Future<DeleteCommentModel> deleteCommentById(
    @Header("user") String? logInUserId,
    @Path("id") String id,
    @Header("brand") String? brandid,
  );

//USer Profile Detail
  @GET('profile/')
  Future<UserProfileDetailModel> getUserProfileDetail({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });

//Edit USer Profile Detail
  @POST('edit-profile/')
  @MultiPart()
  Future<EditProfileResponse> editUserProfile({
    @Header("user") String? logInUserId,
    @Part(name: "profile_image") File? profileImage,
    @Part(name: "name") String? name,
    @Part(name: "username") String? username,
    @Part(name: "bio") String? bio,
    @Part(name: "dob") String? dob,
    @Part(name: "mobile") String? mobile,
    @Part(name: "gender") String? gender,
    @Part(name: "country") String? country,
    @Part(name: "state") String? state,
    @Part(name: "city") String? city,
    @Header("brand") String? brandid,
  });
//Qr Generate API
  @GET('share-profile-qr/')
  Future<QrResponseModel> getShareProfileQr({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
// Discover Post API
  @GET('get-all-post/?')
  Future<PostResponseModel> getDisvoerposts(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Header("brand") String? brandid,
  );

  @POST('onesignal-id/')
  Future<ResetPasswordResponseModel> sendoneSignalIdApi({
    @Header("user") String? logInUserId,
    @Field('onesignal_id') String? onesignalId,
    @Header("brand") String? brandid,
  });

  @GET('notification/?')
  Future<GetNotificationListModel> getNotificationListApi(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Header("brand") String? brandid,
  );

// Get Save post
  @GET('save-post/')
  Future<SavePostModel> getSavePost(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Header("brand") String? brandid,
  );
  // Delete Post
  @GET("delete-post/")
  Future<DeletePostModel> deletePost(
    @Header("user") String? logInUserId,
    @Query("post_id") int postId,
    @Header("brand") String? brandid,
  );
  // Get Scheduled API
  @GET("scheduled-posts/")
  Future<ScheduledPostModel> getScheduledPosts({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
  @GET("remove-scheduled-posts")
  Future<DeletePostModel> deletescheduledPost(
    @Header("user") String? logInUserId,
    @Query("post_id") int postId,
    @Header("brand") String? brandid,
  );
  // Analytics

  @GET('analytics-data')
  Future<AnalyticsModel> getAnalyticsData(
    @Header("user") String? logInUserId,
    @Query('post_id') int postId,
    @Header("brand") String? brandid,
  );
  // User Profile by Id
  @GET('profile')
  Future<UserProfileDetailModel> getprofilebyid(
    @Header("user") String? logInUserId,
    @Query('user_id') int postId,
    @Header("brand") String? brandid,
  );
  @GET('user-posts/')
  Future<PostResponseModel> getuserPostbyId(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Query("user_id") int userId,
    @Header("brand") String? brandid,
  );
  @GET('user-posts/')
  Future<PostResponseModel> getUserposts(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Header("brand") String? brandid,
  );
  @GET('profile')
  Future<UserProfileDetailModel> getprofile({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
  // Get All Video
  @GET('get-user-reel-post/')
  Future<VideoResponseModel> getprofileVideo(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Header("brand") String? brandid,
  );
  @GET('get-user-reel-post/')
  Future<VideoResponseModel> getuserVideobyId(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Query("user_id") int userId,
    @Header("brand") String? brandid,
  );

// Instagram API
  @GET('instagram-url/')
  Future<SocialConnectModel> connectInstagram(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
  @GET('disconnect-instagram/')
  Future<SocialDisconnectModel> disconnectInstagram(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
// Facebook API
  @GET('facebook-url/')
  Future<SocialConnectModel> connectFacebook(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
  @GET('disconnect-facebook/')
  Future<SocialDisconnectModel> disconnectFacebook(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
// Thread API
  @GET('thread-url/')
  Future<SocialConnectModel> connectThread(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
  @GET('disconnect-thread/')
  Future<SocialDisconnectModel> disconnectThread(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
// Youtube API
  @GET('youtube-url/')
  Future<SocialConnectModel> connectYoutube(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
  @GET('disconnect-youtube/')
  Future<SocialDisconnectModel> disconnectYoutube(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
// Tiktok API
  @GET('tiktok-url/')
  Future<SocialConnectModel> connectTiktok(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
  @GET('disconnect-tiktok/')
  Future<SocialDisconnectModel> disconnectTiktok(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
  // X (Twitter) API
  @GET('twitter-login/')
  Future<SocialConnectModel> connectX(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
  @GET('disconnect-twitter/')
  Future<SocialDisconnectModel> disconnectX(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
  @POST('purchase-x/')
  Future<PurchaseXModel> purchaseX({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
  // Telegram API
  @POST('telegram-send-code/')
  Future<TelegramSendCodeModel> connectTelegramSendCode(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
    @Part(name: "phone_number") String? mobileNo,
  );
  @POST('telegram-sign-in/')
  Future<TelegramSignInModel> connectTelegramSignIn(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
    @Part(name: "phone_number") String? mobileNo,
    @Part(name: "code") String? code,
    @Part(name: "phone_code_hash") String? phoneCodeHash,
  );
  @POST('telegram-logout/')
  Future<SocialDisconnectModel> disconnectTelegram(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
  // Mastodon API
  @POST('mastodon-url/')
  Future<SocialConnectModel> connectMastodon({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
    @Part(name: 'instance_url') String instanceUrl = "https://mastodon.social",
  });
  @GET('disconnect-mastodon/')
  Future<SocialDisconnectModel> disconnectMastodon(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );
  // Get Share Profile
  @GET("share-profile/")
  Future<ShareProfileModel> getShareProfile(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  );

  //Uploaded Edit Post
  @POST("update-post/")
  Future<EditPostModel> editPost(
    @Header("user") String? logInUserId,
    @Field("post_id") String postId,
    @Field("title") String title,
    @Field("description") String description,
    @Header("brand") String? brandid,
  );
  //Preofile Data
  @GET("threads-profile/")
  Future<ThreadProfileModel> getThreadProfileData({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });

  // Story
  @GET('get-story-test/')
  Future<NewStoryModel> getNewStoryApi({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });

  @MultiPart()
  @POST('upload-story/')
  Future<UploadStoryModel> uploadStoryApi(
    @Header("user") String? logInUserId,
    @Header("brand") int brandid,
    @Part(name: 'upload_files') File uploadFiles,
    @Part(name: 'music') String music,
    @Part(name: 'title') String title,
    @Part(name: 'instagram') bool instagram,
    @Part(name: 'facebook') bool facebook,
  );
  @MultiPart()
  @POST('upload-story/')
  Future<UploadStoryModel> uploadStoryasHighlightApi(
    @Header("user") String? logInUserId,
    @Part(name: 'upload_files') File uploadFiles,
    @Part(name: 'music') String music,
    @Part(name: 'title') String title,
    @Header("brand") String? brandid,
  );

  @GET("delete-story/")
  Future<DeleteStoryModel> deleteStory(
    @Header("user") String? logInUserId,
    @Query("story_id") String storyId,
    @Header("brand") String? brandid,
  );

  //Chat Module

  @GET("chat-list/?")
  Future<ChatListModel> getChatListApi(
    @Header("user") String? logInUserId,
    @Header("brand") int brandId,
    @Query("page") int page,
  );

  @GET("chat-message-list/?")
  Future<ChatMessageListModel> getChatMessageListApi(
    @Header("user") String? logInUserId,
    @Query("chat_user_id") String userId,
    @Query("page") int page,
    @Header("brand") String? brandid,
  );

  @GET("delete-chat-list/?")
  Future<ResetPasswordResponseModel> deleteChatApi(
    @Header("user") String? logInUserId,
    @Query("user_id") String userId,
    @Header("brand") String? brandid,
  );

  @GET('highlight-view/')
  Future<NewStoryModel> getHighlight({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
    @Query("user_id") String? userId,
  });
  @GET('get-tagged-post/')
  Future<TagPostmodel> getTagPost(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Header("brand") String brandid,
  );

  @GET('follow-list/')
  Future<GetFollowerListModel> getfollowListApi(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Header("brand") String brandid,
  );
  @GET('following-list/')
  Future<GetFollowerListModel> getfollowingListApi(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Header("brand") String brandid,
  );
  @GET("following-list/")
  Future<GetFollowerListModel> getfollowingListbyIdApi(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Query("user_id") int userId,
    @Header("brand") String brandid,
  );
  @GET("follow-list/")
  Future<GetFollowerListModel> getfollowListbyIdApi(
    @Header("user") String? logInUserId,
    @Query("page") int page,
    @Query("user_id") int userId,
    @Header("brand") String brandid,
  );

  // MARK:Select UserType And Industry Type
  @POST("industry/")
  Future<SelectUserAndIndustryType> selectUserAndIndustryType(
    @Header("user") String? logInUserId,
    @Part(name: "industry") String industry,
    @Part(name: "type") String type,
    @Header("brand") String brandid,
  );
  @GET('get-industry/')
  Future<GetUserAndIndustryType> getUserAndIndustryType({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
  @GET('user-status/')
  Future<GetUserStatus> getUserStatus({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
  // Get Brands
  @GET("get-brands/")
  Future<GetBrandsModel> getBrandsbyId(
    @Header("user") String? logInUserId,
    @Header("brand") int brandId,
  );

  // Delete Brands
  @POST("delete-brand/")
  Future<ResetPasswordResponseModel> getDeleteBrandsbyId(
    @Header("user") String? logInUserId,
    @Part(name: "brand_id") int? brandId,
  );

  // Edit Brand By ID
  @MultiPart()
  @POST("edit-brand/")
  Future<EditBrandByIdModel> editBrand({
    @Header("user") String? logInUserId,
    @Part(name: "name") String? name,
    @Part(name: "email") String? email,
    @Part(name: "domain") String? domain,
    @Part(name: "logo_file") File? logoFile,
    @Part(name: "brand_id") int? brandId,
  });

// Register Brand
  @MultiPart()
  @POST("register-brand/")
  Future<RegisterBrandModel> registerBrand({
    @Header("user") String? logInUserId,
    @Header("subscription") String? subscriptionId,
    @Part(name: "name") String? name,
    @Part(name: "email") String? email,
    @Part(name: "domain") String? domain,
    @Part(name: "logo_file") File? logoFile,
  });

//Get Brands
  @GET("get-brands/")
  Future<GetBrandsModel> getBrands({
    @Header("user") String? logInUserId,
  });

  //Survey API
  @POST("survey/")
  Future<SurveyFormModel> surveyFormApi(
    @Header("user") String? logInUserId,
    @Body() Map<String, dynamic> body,
    @Header("brand") String brandid,
  );

  @GET("user-data-home/")
  Future<UserHomeDataModel> getuserHomeDataApi({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });

  // User Management
  // get invited user API
  @GET("get-invited-users/")
  Future<GetInvitedUserModel> getInvitedUserAPI({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
  // get invitee user API
  @GET("get-invitee-users/")
  Future<GetInviteeUserModel> getInviteeUserAPI({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
  //Invite User API
  @POST("user-management/")
  Future<ResetPasswordResponseModel> inviteUser(@Header("user") String? logInUserId, @Header("brand") String brandid,
      @Header("subscription") String subscriptionId, @Body() Map<String, dynamic> request);
  // Get User Role API
  @GET("user-roles/")
  Future<GetUserRolesModel> getUserRoles({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
  // Get User Permissions API
  @GET("test-permissions/")
  Future<PermissionModel> getUserPermissions({
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
  });
  // Add  User Role API
  @POST("user-roles/")
  Future<ResetPasswordResponseModel> createUserRole(
      @Header("user") String? logInUserId, @Header("brand") String brandid, @Body() Map<String, dynamic> body);

  @POST("update-subscription/")
  Future<UpdateSurveyModel> updatesurveyApi(
    @Header("user") String? logInUserId,
    @Header("brand") String brandid,
    @Body() Map<String, dynamic> body,
  );

  @GET('social-platforms/')
  Future<DynamicPlatformModel> dynamicplatformLoading(
    @Header("user") String? logInUserId,
    @Header("brand") String brandid,
  );
  @POST("feedback/")
  @MultiPart()
  Future<ResetPasswordResponseModel> feedbackApi({
    @Header("user") String? logInUserId,
    @Header("brand") int? brandid,
    @Part(name: "app_using_frequency") String? frequency,
    @Part(name: "description") String? description,
    @Part(name: "stars") String? stars,
    @Part(name: "file") File? file,
  });
  @GET('current-brand/')
  Future<GetCurentBrandIdModel> getCurentBrandID(
    @Header("user") String? logInUserId,
    @Header("brand") String brandid,
  );
  @POST("block-user/")
  Future<BlockUserModel> blockUser(
    @Header("user") String? logInUserId,
    @Header("brand") int brandid,
    @Body() Map<String, dynamic> body,
  );
  @GET("blocked-users/")
  Future<BlockedUsersModel> getBlockedUsers(
    @Header("user") String? logInUserId,
    @Header("brand") int brandid,
  );
  @GET('demography-list/')
  Future<DemographyResponseModel> getDemographyList(
    @Header("user") String? logInUserId,
    @Header("brand") String? brandid,
    @Query('request_type') int requestType,
    @Query('forwarding_id') int? forwardingId,
  );
  @GET("hashtag-posts/")
  Future<HashtagPostModel> getHashtagPostApi(
    @Header("user") String? logInUserId,
    @Query("hashtag_id") String hashtagId,
    @Header("brand") String? brandid,
  );

  @GET("search-location/")
  Future<SearchLocationModel> searchLocation({
    @Query("search_text") String? search,
    @Query("lat") String? lat,
    @Query("long") String? long,
  });

  // version update
  @GET("app-version/")
  Future<VersionModel> getVersionUpdate({@Query("version_id") String? platform});

  @POST("register-beta-user/")
  Future<BetaUserSignupModel> registerBetaUser({
    @Field("name") String? name,
    @Field("email") String? email,
    @Field("age") String? age,
    @Field("mobile") String? mobile,
    @Field("platform_status") String? platformStatus,
  });

  @GET("all-chat-list/")
  Future<SmchatListModel> getallsmchatList(
    @Header("user") String? logInUserId,
    @Header("brand") int brandId,
  );
  @GET("single-chat-details/")
  Future<SingleChatModel> getsinglechatDetails(
    @Header("user") String? logInUserId,
    @Header("brand") int brandId,
    @Header("platform") int platformId,
    @Header("conversation") String conversationId,
    @Header("owner") String ownerId,
  );

  @MultiPart()
  @POST("send-instagram-message/")
  Future<SendinstagreamMessageModel> sendInstagreamMessageWithoutFile({
    @Header("user") String? logInUserId,
    @Header("brand") int? brandId,
    @Part(name: "from_user_id") String? fromuserId,
    @Part(name: "to_user_id") String? touserId,
    @Part(name: "message_type") int? messageType,
    @Part(name: "message") String? message,
  });

  @MultiPart()
  @POST("send-instagram-message/")
  Future<SendinstagreamMessageModel> sendInstagreamMessageWithFile({
    @Header("user") String? logInUserId,
    @Header("brand") int? brandId,
    @Part(name: "from_user_id") String? fromuserId,
    @Part(name: "to_user_id") String? touserId,
    @Part(name: "message_type") int? messageType,
    @Part(name: "message") String? message,
    @Part(name: "file") File? file,
  });
  @MultiPart()
  @POST("send-facebook-message/")
  Future<SendinstagreamMessageModel> sendFacebookMessageWithoutFile({
    @Header("user") String? logInUserId,
    @Header("brand") int? brandId,
    @Part(name: "from_user_id") String? fromuserId,
    @Part(name: "to_user_id") String? touserId,
    @Part(name: "message_type") int? messageType,
    @Part(name: "message") String? message,
  });

  @MultiPart()
  @POST("send-facebook-message/")
  Future<SendinstagreamMessageModel> sendFacebookMessageWithFile({
    @Header("user") String? logInUserId,
    @Header("brand") int? brandId,
    @Part(name: "from_user_id") String? fromuserId,
    @Part(name: "to_user_id") String? touserId,
    @Part(name: "message_type") int? messageType,
    @Part(name: "message") String? message,
    @Part(name: "file") File? file,
  });
  @GET("telegram-dialogs/")
  Future<TelegramUserListModel> getTelegramUerList({
    @Header("user") String? logInUserId,
    @Header("brand") int? brandId,
  });
  @GET("telegram-user-chats/")
  Future<TelegramUserChatListModel> getTelegramUerChatList({
    @Header("user") String? logInUserId,
    @Header("brand") int? brandId,
    @Query("target_user_id") String? targetUserId,
  });
  @MultiPart()
  @POST("telegram-send-message/")
  Future<TelegramSandMassageModel> telegramSendMassage({
    @Header("user") String? logInUserId,
    @Header("brand") int? brandId,
    @Part(name: "chat_id") String? targetUserId,
    @Part(name: "message") String? massage,
  });

  @GET('post-industry/')
  Future<PostIndustryModel> getPostIndustryDetails();
  // Get Draft Post
  @GET('get-draft-post/')
  Future<DraftPostModel> getDraftPost(
      {@Header("user") String? logInUserId, @Header("brand") int? brandId, @Query("page") int? page});
  @GET("draft-delete/{id}")
  Future<DeleteDraftPostModel> deleteDraftPost({
    @Header("user") String? logInUserId,
    @Path("id") int? id,
    @Header("brand") int? brand,
  });
  @POST("/draft-post-upload/")
  Future<UploadDraftPostModel> uploadDraftPost({
    @Header("user") String? logInUserId,
    @Header("brand") int? brand,
    @Field("post_id") int? postId,
  });
  // Linkdin Analytics
  @POST("linkedin-analytics-g1/")
  Future<FollowerImpretionPostModel> getLinkedInFollowerImpretionPostAnalytics({
    @Header("user") String? logInUserId,
    @Part(name: "start_date") String? startDate,
    @Part(name: "end_date") String? endDate,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("linkedin-analytics-g2/")
  Future<LinkedinPieChartModel> getLinkedInPieChartAnalytics({
    @Header("user") String? logInUserId,
    @Part(name: "start_date") String? startDate,
    @Part(name: "end_date") String? endDate,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("linkedin-analytics-g3/")
  Future<LinkedinEngagementAndShareModel> getLinkedinEngagementandShare({
    @Header("user") String? logInUserId,
    @Part(name: "start_date") String? startDate,
    @Part(name: "end_date") String? endDate,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  // Instagram Analytics
  @POST("instagram-analytics-g1/")
  Future<InstagramLineGraph> getInstagramLinegraph({
    @Header("user") String? logInUserId,
    @Part(name: "start_date") String? startDate,
    @Part(name: "end_date") String? endDate,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("instagram-analytics-g2/")
  Future<InstagramPieChartModel> getInstagramPiechart({
    @Header("user") String? logInUserId,
    @Part(name: "start_date") String? startDate,
    @Part(name: "end_date") String? endDate,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  // Facebook AnalyticsModel
  @POST("facebook-analytics-g1/")
  Future<FacebookImpretionAndFollowerModel> getFacebookImpressionFollower({
    @Header("user") String? logInUserId,
    @Part(name: "start_date") String? startDate,
    @Part(name: "end_date") String? endDate,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("facebook-analytics-g2/")
  Future<FacebookPostGraphModel> getFacebookPostShareComment({
    @Header("user") String? logInUserId,
    @Part(name: "start_date") String? startDate,
    @Part(name: "end_date") String? endDate,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  // Youtube Analytics
  @POST("youtube-analytics-g1/")
  Future<YoutubeVideoGraphModel> getYoutubeVideoGraph({
    @Header("user") String? logInUserId,
    @Part(name: "start_date") String? startDate,
    @Part(name: "end_date") String? endDate,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("youtube-analytics-g2/")
  Future<YoutubeSubscribeLikeCommentModel> getYoutubeSubscribeLikeCommentGraph({
    @Header("user") String? logInUserId,
    @Part(name: "start_date") String? startDate,
    @Part(name: "end_date") String? endDate,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("threads-analytics-g5/")
  Future<ThreadLineGraphModel> getThreadLineGraph({
    @Header("user") String? logInUserId,
    @Part(name: "start_date") String? startDate,
    @Part(name: "end_date") String? endDate,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("threads-analytics-g1/")
  Future<ThreadCountryModel> getThreadCountry({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("threads-analytics-g2/")
  Future<ThreadCountryModel> getThreadcity({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("threads-analytics-g3/")
  Future<ThreadCountryModel> getThreadage({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("threads-analytics-g4/")
  Future<ThreadCountryModel> getThreadgender({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("pinterest-analytics-g2/")
  Future<PinterestAnalyticsModel> getPinterestLineGraph({
    @Header("user") String? logInUserId,
    @Part(name: "start_date") String? startDate,
    @Part(name: "end_date") String? endDate,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @GET('pinterest-analytics-g1/')
  Future<PnterestTotalCountModel> getPintresttotalcount({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
    @Header("subscription") String? subscription,
  });
  @POST("share/")
  Future<PostShareModel> postShare({
    @Header("user") String? logInUserId,
    @Part(name: "post_id") String? postId,
    @Part(name: "share_type") String? shareType,
    @Header("brand") String? brand,
  });
  @GET("get-post/")
  Future<GetPostbyIdModel> getPostById({
    @Header("user") String? logInUserId,
    @Query("post_id") String? postId,
    @Header("brand") String? brand,
  });
  @POST("share-post-message/")
  Future<SharePostMessageModel> sharePostMessage({
    @Header("user") String? logInUserId,
    @Part(name: "post_id") String? postId,
    @Part(name: "to_user_id") String? toUserId,
    @Header("brand") String? brand,
  });

  @POST("approve-invite/")
  Future<ForgotPasswordResponseModel> approveInvite({
    @Header("user") String? logInUserId,
    @Part(name: "invite_id") int? inviteId,
    @Header("brand") String? brand,
  });

  @POST("decline-invite/")
  Future<ForgotPasswordResponseModel> declineInvite({
    @Header("user") String? logInUserId,
    @Part(name: "decline_id") int? declineId,
    @Header("brand") String? brand,
  });

  @POST("revoke-invite/")
  Future<ForgotPasswordResponseModel> revokeInvite({
    @Header("user") String? logInUserId,
    @Part(name: "invite_id") int? inviteId,
    @Header("brand") String? brand,
  });

  @GET("get-analytics-platforms/")
  Future<GetAnalyticsPlatformsModel> getAnalyticsPlatforms({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
  });

  @POST("notification-delete/")
  Future<ForgotPasswordResponseModel> deleteNotification({
    @Header("user") String? logInUserId,
    @Part(name: "notification_id") int? notificationId,
    @Header("brand") String? brand,
  });

  @POST("logout/")
  Future<ForgotPasswordResponseModel> logOut();

  @GET("reward-screen/")
  Future<WalletModel> getWalletData({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
  });

  @GET("leaderboard/")
  Future<LeaderBoardModel> getLeaderboardData({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
  });

  @GET('user-text-posts/')
  Future<GetUserTextPostModel> getUserTextpostAPI({
    @Header("user") String? logInUserId,
    @Query("page") int? page,
    @Header("brand") String? brand,
  });

  @GET('user-text-posts/')
  Future<GetUserTextPostModel> getUserByIdTextpostAPI({
    @Header("user") String? logInUserId,
    @Query("page") int? page,
    @Query("user_id") int? userId,
    @Header("brand") String? brand,
  });

  @POST("update-text-post/")
  Future<EditPostModel> editTextPost({
    @Header("user") String? logInUserId,
    @Part(name: "post_id") String? postId,
    @Part(name: "title") String? title,
    @Part(name: "description") String? description,
    @Header("brand") String? brand,
  });

  // @GET('get-reel-post/?')
  // Future<ReelsModel> getReels(
  //   @Query("page") int? page,
  // );

  @GET('get-reward-data/')
  Future<GetRewardDataModel> getRewardData({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
  });
  @GET('live-users/')
  Future<LiveUserModel> getliveuserList({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
  });
  //MARK:======
  @GET('switch-user-account')
  Future<SwitchUserAccountModel> getSwitchUser({
    @Header("brand") String? brand,
    @Header("user") String? logInUserId,
    @Header("onesignalplayer") String? onesignalplayer,
  });
  @GET('scheduled-posts-web/')
  Future<ScheduledPostsWebModel> getScheduledPostWeb({
    @Header("user") String? logInUserId,
    @Query("user_id") int? userId,
    @Header("brand") String? brand,
  });
  @POST('ai-generate-response/')
  Future<AIgenerateContentModel> generatePostContent({
    @Header("user") String? logInUserId,
    @Part(name: 'prompt') String? prompt,
    @Part(name: 'is_seeking_generation') String? isSeekingGeneration,
    @Part(name: "media") File? media,
    @Header("brand") String? brand,
  });
  @GET('web-list/')
  Future<PlannerListPostModel> getWebList({
    @Header("user") String? logInUserId,
    @Query('platform') String? platform,
    @Query("page") int? page,
    @Header("brand") String? brand,
  });
  @POST('ai-generate-duplicate-post/')
  Future<GenerateDuplicatePostModel> createDuplicatePost({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
    @Part(name: 'media') File? media,
    @Part(name: 'prompt') String? prompt,
  });

  @GET('highlights-delete/')
  Future<ForgotPasswordResponseModel> highlightsDelete({
    @Query("id") int? id,
  });

  @POST("approve-post-upload/")
  Future<ForgotPasswordResponseModel> approvePendingPostUpload({
    @Part(name: "post_id") int? postId,
  });

  @POST("reject-post-upload/")
  Future<ForgotPasswordResponseModel> rejectPendingPostUpload({
    @Part(name: "post_id") int? postId,
  });

  @GET("pending-post-approval/")
  Future<PandingAprovelPostModel> pendingPostApproval();
  // MARK; Ai generate message reply
  @POST('ai-generate-message-reply/')
  Future<AiGenerateMassageOrComment> aiGenerateMassage({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
    @Part(name: 'message_text') String? massageText,
  });
  @POST('ai-generate-comment/')
  Future<AiGenerateMassageOrComment> aiGenerateComment({
    @Header("user") String? logInUserId,
    @Header("brand") String? brand,
    @Part(name: 'comment_text') String? ccommentText,
  });
}
