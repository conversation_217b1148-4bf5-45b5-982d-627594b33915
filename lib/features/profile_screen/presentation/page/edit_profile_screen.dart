import 'dart:developer';
import 'dart:io';
import 'package:custom_image_crop/custom_image_crop.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/profile_screen/model/demography_response_model.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/edit_profile_screen_shimmer.dart';
import 'package:flowkar/features/widgets/custom/custom_dropown.dart';

import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider(
      create: (_) => UserProfileBloc()..add(UserProfileInitial()),
      child: const EditProfileScreen(),
    );
  }

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> with TickerProviderStateMixin {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final CustomImageCropController cropController = CustomImageCropController();
  File profileFilePath = File('');
  bool isProfileEmpty = false;
  // TextEditingController countryController = TextEditingController();
  // TextEditingController stateController    = TextEditingController();
  // TextEditingController cityController    = TextEditingController();
  String? selectedCountry;
  String? selectedState;
  String? selectedCity;

  final bioMaxLength = 200;

  final GlobalKey<FormFieldState> stateFieldKey = GlobalKey<FormFieldState>();
  final GlobalKey<FormFieldState> cityFieldKey = GlobalKey<FormFieldState>();

  bool isLoading = false;
  bool isSaveLoading = false;
  bool iserror = false;

  Future<File> urlToFile(String imageUrl) async {
    final response = await http.get(Uri.parse(imageUrl));
    final documentDirectory = await getApplicationDocumentsDirectory();
    final filePath = '${documentDirectory.path}/profile_image.jpg';
    final file = File(filePath);
    await file.writeAsBytes(response.bodyBytes);
    return file;
  }

  Future<File> copyAssetSvgToTempFile() async {
    final byteData = await rootBundle.load(AssetConstants.pngUserReomve);

    final tempDir = await getTemporaryDirectory();
    final tempFile = File('${tempDir.path}/png_user_bg.png');

    await tempFile.writeAsBytes(byteData.buffer.asUint8List());

    return tempFile; // Return File directly
  }

  bool isImageLoading = false;
  @override
  void initState() {
    final userProfileState = context.read<UserProfileBloc>().state;
    userProfileState.bioController?.addListener(() {
      setState(() {});
    });
    super.initState();

    context.read<UserProfileBloc>().add(LoadCountriesEvent());
    context.read<UserProfileBloc>().add(FetchUserProfileEvent(onEdit: true));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: (context, state) {
        if (state.isReconnected) {
          context.read<UserProfileBloc>().add(LoadCountriesEvent());
          context.read<UserProfileBloc>().add(FetchUserProfileEvent(onEdit: true));
        }
      },
      child: BlocBuilder<UserProfileBloc, UserProfileState>(
        builder: (context, state) {
          Logger.lOG("profiel File in appbar ${profileFilePath.path}");
          return Scaffold(
            appBar: _buildAppBar(
              context: context,
              id: state.userProfile?.data.id ?? 0,
              nameController: state.nameController ?? TextEditingController(),
              formKey: formKey,
              userNameController: state.userNameController ?? TextEditingController(),
              emailController: state.emailController ?? TextEditingController(),
              dobController: state.dobController ?? TextEditingController(),
              mobileNumberController: state.mobileNumberController ?? TextEditingController(),
              bioController: state.bioController ?? TextEditingController(),
              selectedGender: state.selectedGender,
              profilePhoto: profileFilePath.path,
              state: state,
              countryController: state.countryController ?? TextEditingController(),
              stateController: state.stateController ?? TextEditingController(),
              cityController: state.cityController ?? TextEditingController(),
            ),
            body: BlocBuilder<ConnectivityBloc, ConnectivityState>(
              builder: (context, connectivityState) {
                if (!connectivityState.isConnected && state.userProfile == null) {
                  return EditProfileScreenShimmer();
                } else if (state.loading) {
                  return EditProfileScreenShimmer();
                } else if (state.userProfile == null) {
                  return ListView(
                    physics: AlwaysScrollableScrollPhysics(),
                    children: [
                      buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                      ExceptionWidget(
                        imagePath: Assets.images.svg.exception.svgNodatafound.path,
                        showButton: false,
                        title: Lang.of(context).lbl_no_data_found,
                        subtitle: Lang.of(context).lbl_no_post,
                      ),
                    ],
                  );
                } else {
                  selectedCountry = state.selectedCountry?.name;
                  return Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: InkWell(
                            focusColor: Colors.transparent,
                            onTap: () {
                              FocusManager.instance.primaryFocus?.unfocus();
                            },
                            child: Column(
                              children: [
                                _buildProfilePhoto(context, state),
                                _buildEditProfileForm(
                                  context: context,
                                  nameController: state.nameController ?? TextEditingController(),
                                  nameFocusNode: state.nameFocusNode ?? FocusNode(),
                                  userNameFocusNode: state.userNameFocusNode ?? FocusNode(),
                                  formKey: formKey,
                                  userNameController: state.userNameController ?? TextEditingController(),
                                  emailFocusNode: state.emailFocusNode ?? FocusNode(),
                                  emailController: state.emailController ?? TextEditingController(),
                                  bioFocusNode: state.bioFocusNode,
                                  mobileNumberFocusNode: state.mobileNumberFocusNode ?? FocusNode(),
                                  mobileNumberController: state.mobileNumberController ?? TextEditingController(),
                                  bioController: state.bioController ?? TextEditingController(),
                                  selectedGender: state.selectedGender,
                                  dobController: state.dobController ?? TextEditingController(),
                                  countryController: state.countryController ?? TextEditingController(),
                                  stateController: state.stateController ?? TextEditingController(),
                                  cityController: state.cityController ?? TextEditingController(),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                }
              },
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar({
    required BuildContext context,
    required int id,
    required TextEditingController nameController,
    required GlobalKey<FormState> formKey,
    required TextEditingController userNameController,
    required TextEditingController emailController,
    required TextEditingController dobController,
    required TextEditingController mobileNumberController,
    required TextEditingController bioController,
    required String profilePhoto,
    required String selectedGender,
    required UserProfileState state,
    required TextEditingController countryController,
    required TextEditingController stateController,
    required TextEditingController cityController,
  }) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        Row(
          children: [
            InkWell(
              onTap: () {
                FocusScope.of(context).unfocus();
                NavigatorService.goBack();
              },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomImageView(
                  imagePath: Assets.images.svg.authentication.icBackArrow.path,
                  height: 16.h,
                ),
              ),
            ),
            buildSizedBoxW(20.w),
            SizedBox(
              height: 40.h,
              child: Center(
                child: Text(
                  Lang.of(context).lbl_edit_profile,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
                ),
              ),
            ),
          ],
        ),
      ],
      actions: [
        CustomElevatedButton(
          width: 80.w,
          margin: EdgeInsets.only(bottom: 0.h),
          onPressed: () async {
            setState(() {
              isSaveLoading = true;
            });
            // Force validation of state and city fields if country is selected
            if (context.read<UserProfileBloc>().state.countryController!.text.isNotEmpty) {
              stateFieldKey.currentState?.validate();
              cityFieldKey.currentState?.validate();
            }

            // Validate all form fields
            if (formKey.currentState!.validate()) {
              // If country is selected, check state and city
              if (state.countryController!.text.isNotEmpty) {
                // Collect validation errors
                List<String> errors = [];

                // Check state
                if (state.stateController?.text.isEmpty ?? true) {
                  errors.add('Please select State');
                }

                // Check city
                if (state.cityController?.text.isEmpty ?? true) {
                  errors.add('Please select City');
                }

                // Show all errors together if any
                if (errors.isNotEmpty) {
                  toastification.show(
                    type: ToastificationType.error,
                    showProgressBar: false,
                    title: Text(
                      "Somthing went wrong",
                      style: GoogleFonts.montserrat(
                        fontSize: 12.0.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    autoCloseDuration: const Duration(seconds: 3),
                  );
                  return;
                }
              }

              File? profileImageFile;

              if (profilePhoto.isNotEmpty && profilePhoto != AssetConstants.pngUser) {
                profileImageFile = File(profilePhoto);
              } else if (state.profile.isNotEmpty) {
                profileImageFile = await urlToFile(state.profile.toString());
              } else {
                // profileImageFile = null;
                profileImageFile = await copyAssetSvgToTempFile();
              }

              Logger.lOG("B isProfile $isProfileEmpty");
              Logger.lOG("B profilePhoto.isNotEmpty ${profilePhoto.isNotEmpty}");
              Logger.lOG("B File(profilePhoto) ${File(profilePhoto)}");
              Logger.lOG("B profileImageFile, $profileImageFile");
              context.read<UserProfileBloc>().add(
                    EditUserProfileEvent(
                      userId: id,
                      name: nameController.text.trim(),
                      username: userNameController.text.trim(),
                      bio: bioController.text.trim(),
                      mobile: mobileNumberController.text.trim(),
                      email: emailController.text.trim(),
                      profileImage: profileImageFile, // This will be null when image is removed
                      dob: dobController.text,
                      gender: selectedGender,
                      country: state.countryController!.text,
                      state: state.stateController!.text,
                      city: state.cityController!.text,
                    ),
                  );
              Logger.lOG("isProfile $isProfileEmpty");
              Logger.lOG("profilePhoto.isNotEmpty ${profilePhoto.isNotEmpty}");
              Logger.lOG("File(profilePhoto) ${File(profilePhoto)}");
              Logger.lOG("profileImageFile, ${profileImageFile}");
              setState(() {
                isSaveLoading = false;
              });
              NavigatorService.goBack();
            }
          },
          isLoading: state.loading || state.editloading || isLoading || isSaveLoading,
          isDisabled: state.loading || state.editloading || isLoading || isSaveLoading,
          color: Theme.of(context).primaryColor,
          brderRadius: 10.r,
          decoration: const BoxDecoration(
            boxShadow: [
              BoxShadow(
                blurRadius: 8,
                color: Colors.black12,
                offset: Offset(0, 0),
              ),
            ],
          ),
          text: Lang.of(context).lbl_save,
          buttonTextStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontSize: 14.sp,
                color: Theme.of(context).customColors.white,
                fontWeight: FontWeight.w500,
              ),
          height: 35.h,
        ),
      ],
    );
  }

  Widget _buildProfilePhoto(BuildContext context, UserProfileState state) {
    return Column(
      children: [
        Container(
          height: 80.h,
          width: 80.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(100.r),
            border: Border.all(
              color: profileFilePath.path.isNotEmpty
                  ? Theme.of(context).primaryColor.withOpacity(0.2)
                  : state.profile == ""
                      ? Theme.of(context).primaryColor.withOpacity(0.2)
                      : Theme.of(context).primaryColor.withOpacity(0.05),
              width: 2.w,
            ),
          ),
          child: isLoading
              ? LoadingAnimationWidget()
              : CustomImageView(
                  margin: profileFilePath.path.isNotEmpty
                      ? EdgeInsets.zero
                      : state.profile == ""
                          ? EdgeInsets.all(16)
                          : EdgeInsets.zero,
                  imagePath: profileFilePath.path.isNotEmpty
                      ? profileFilePath.path
                      : state.profile == ""
                          ? AssetConstants.pngUser
                          : state.profile,
                  radius: BorderRadius.circular(profileFilePath.path.isNotEmpty
                      ? 100.r
                      : state.profile == ""
                          ? 0
                          : 100.r),
                  fit: state.profile == "" ? BoxFit.contain : BoxFit.cover,
                ),
        ),
        buildSizedBoxH(16.h),
        GestureDetector(
          onTap: () => _showChangePhotoBottomSheet(context, state),
          child: Text(
            Lang.of(context).lbl_change_profile_photo,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 14.sp, fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }

  void _showChangePhotoBottomSheet(BuildContext context, UserProfileState state) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0.r)),
      ),
      builder: (BuildContext context) {
        return Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.0.r)),
          ),
          margin: EdgeInsets.only(bottom: Platform.isIOS ? 0 : MediaQuery.of(context).viewPadding.bottom),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                height: 3.h,
                width: 40.w,
                decoration: BoxDecoration(
                  color: Color(0xffE9EBEA),
                  borderRadius: BorderRadius.circular(100.r),
                ),
              ),
              buildSizedBoxH(16.h),
              Container(
                height: 80.h,
                width: 80.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.r),
                  border: Border.all(
                    color: profileFilePath.path.isNotEmpty
                        ? Theme.of(context).primaryColor.withOpacity(0.2)
                        : state.profile == ""
                            ? Theme.of(context).primaryColor.withOpacity(0.2)
                            : Theme.of(context).primaryColor.withOpacity(0.05),
                    width: 2.w,
                  ),
                ),
                child: isLoading
                    ? LoadingAnimationWidget()
                    : CustomImageView(
                        margin: profileFilePath.path.isNotEmpty
                            ? EdgeInsets.zero
                            : state.profile == ""
                                ? EdgeInsets.all(16)
                                : EdgeInsets.zero,
                        imagePath: profileFilePath.path.isNotEmpty
                            ? profileFilePath.path
                            : state.profile == ""
                                ? AssetConstants.pngUser
                                : state.profile,
                        radius: BorderRadius.circular(profileFilePath.path.isNotEmpty
                            ? 100.r
                            : state.profile == ""
                                ? 0
                                : 100.r),
                        fit: state.profile == "" ? BoxFit.contain : BoxFit.cover,
                      ),
              ),
              buildSizedBoxH(10.h),
              Text(
                Lang.of(context).lbl_change_profile_photo,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w700,
                    ),
              ),
              buildSizedBoxH(16.h),
              // Padding(
              //   padding: EdgeInsets.only(
              //     bottom: 16.h,
              //     left: 16.w,
              //     right: 16.w,
              //   ),
              //   child: GestureDetector(
              //     onTap: () async {
              //       NavigatorService.goBack();

              //       final ImagePicker picker = ImagePicker();
              //       final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);

              //       if (pickedFile != null) {
              //         await _cropImage(pickedFile.path);
              //         // setState(() {
              //         //   profileFilePath = pickedFile.path.isNotEmpty ? File(pickedFile.path) : File('');
              //         // });
              //       }
              //     },
              //     child: Container(
              //       height: 40.h,
              //       width: double.infinity,
              //       decoration: BoxDecoration(
              //         borderRadius: BorderRadius.circular(100.r),
              //         color: Theme.of(context).primaryColor.withOpacity(0.08),
              //       ),
              //       child: Center(
              //         child: Text(
              //           Lang.of(context).lbl_new_profile_photo,
              //           style: Theme.of(context).textTheme.titleLarge?.copyWith(
              //                 fontSize: 18.sp,
              //                 fontWeight: FontWeight.bold,
              //               ),
              //           textAlign: TextAlign.center,
              //         ),
              //       ),
              //     ),
              //   ),
              // ),
              Padding(
                padding: EdgeInsets.only(
                  left: 16.w,
                  right: 16.w,
                ),
                child: GestureDetector(
                  onTap: () async {
                    NavigatorService.goBack();

                    final ImagePicker picker = ImagePicker();
                    final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);

                    if (pickedFile != null) {
                      await _cropImage(pickedFile.path);
                      // setState(() {
                      //   profileFilePath = pickedFile.path.isNotEmpty ? File(pickedFile.path) : File('');
                      // });
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.symmetric(vertical: 6.h),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(30.r),
                    ),
                    child: ListTile(
                      minTileHeight: 60.h,
                      leading: CustomImageView(
                        height: 20.h,
                        width: 20.w,
                        imagePath: AssetConstants.pngUserEditImage,
                        fit: BoxFit.contain,
                      ),
                      title: Text(
                        Lang.of(context).lbl_change_profile_photo,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                ),
              ),
              buildSizedBoxH(5.h),

              // Padding(
              //   padding: EdgeInsets.only(
              //     bottom: 16.h,
              //     left: 16.w,
              //     right: 16.w,
              //   ),
              //   child: GestureDetector(
              //     onTap: () async {
              //       NavigatorService.goBack();
              //       context.read<UserProfileBloc>().add(RemoveImageFromProfile(image: ''));
              //       setState(() {
              //         isProfileEmpty = true;
              //         profileFilePath = File(''); // Clear local file
              //         // profilePhoto = '';
              //       });

              //       // setState(() {
              //       //   profileFilePath = pickedFile.path.isNotEmpty ? File(pickedFile.path) : File('');
              //       // });
              //     },
              //     child: Container(
              //       height: 40.h,
              //       width: double.infinity,
              //       decoration: BoxDecoration(
              //         borderRadius: BorderRadius.circular(100.r),
              //         color: Theme.of(context).primaryColor.withOpacity(0.08),
              //       ),
              //       child: Center(
              //         child: Text(
              //           Lang.of(context).lbl_remove_profile_photo,
              //           style: Theme.of(context).textTheme.titleLarge?.copyWith(
              //                 fontSize: 18.sp,
              //                 fontWeight: FontWeight.bold,
              //               ),
              //           textAlign: TextAlign.center,
              //         ),
              //       ),
              //     ),
              //   ),
              // ),
              Padding(
                padding: EdgeInsets.only(
                  left: 16.w,
                  right: 16.w,
                ),
                child: GestureDetector(
                  onTap: () {
                    NavigatorService.goBack();
                    context.read<UserProfileBloc>().add(RemoveImageFromProfile(image: ''));
                    setState(() {
                      isProfileEmpty = true;
                      profileFilePath = File(''); // Clear local file
                      // profilePhoto = '';
                    });

                    // setState(() {
                    //   profileFilePath = pickedFile.path.isNotEmpty ? File(pickedFile.path) : File('');
                    // });
                  },
                  child: Container(
                    margin: EdgeInsets.symmetric(vertical: 6.h),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(30.r),
                    ),
                    child: ListTile(
                      minTileHeight: 60.h,
                      leading: CustomImageView(
                        height: 20.h,
                        width: 20.w,
                        imagePath: AssetConstants.pngUserDeleteImage,
                        fit: BoxFit.contain,
                      ),
                      title: Text(
                        Lang.of(context).lbl_remove_profile_photo,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEditProfileForm({
    required BuildContext context,
    required TextEditingController nameController,
    required FocusNode nameFocusNode,
    required FocusNode userNameFocusNode,
    required GlobalKey<FormState> formKey,
    required TextEditingController userNameController,
    required FocusNode emailFocusNode,
    required TextEditingController emailController,
    required FocusNode bioFocusNode,
    required FocusNode mobileNumberFocusNode,
    required TextEditingController mobileNumberController,
    required TextEditingController bioController,
    required String selectedGender,
    required TextEditingController dobController,
    required TextEditingController countryController,
    required TextEditingController stateController,
    required TextEditingController cityController,
  }) {
    return Form(
      key: formKey,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildNameInputField(context, nameController, nameFocusNode, userNameFocusNode),
            buildSizedBoxH(20.h),
            _buildUserNameInputField(context, userNameController, userNameFocusNode, emailFocusNode),
            buildSizedBoxH(20.h),
            _buildEmailInputField(context, emailController, emailFocusNode, bioFocusNode),
            buildSizedBoxH(20.h),
            _buildBioInputField(context, bioController, bioFocusNode),
            buildSizedBoxH(20.h),
            _buildDateOfBirthInputField(context, dobController),
            buildSizedBoxH(20.h),
            _buildMobileNumberInputField(context, mobileNumberController, mobileNumberFocusNode),
            buildSizedBoxH(20.h),
            _buildGenderDropDown(context, mobileNumberFocusNode),
            buildSizedBoxH(20.h),
            // _buildCountrySelect(context),
            _buildCountrySelect(context),
            buildSizedBoxH(20.h),
            // _buildStateSelect(stateController),
            _buildStateSelect(context),
            buildSizedBoxH(20.h),
            // _buildCitySelect(cityController),
            _buildCitySelect(context),
            buildSizedBoxH(20.h),
          ],
        ),
      ),
    );
  }

  Widget _buildNameInputField(
    BuildContext context,
    TextEditingController nameController,
    FocusNode nameFocusNode,
    FocusNode userNameFocusNode,
  ) {
    return FlowkarTextFormField(
      fillColor: Theme.of(context).customColors.white,
      filled: true,
      context: context,
      isCapitalized: true,
      // hintText: Lang.of(context).lbl_name,
      labelText: Lang.of(context).lbl_name,
      contentPadding: EdgeInsets.all(16.0),
      controller: nameController,
      focusNode: nameFocusNode,
      validator: AppValidations.validateName,
      textInputAction: TextInputAction.next,
      onFieldSubmitted: (_) {
        FocusScope.of(context).requestFocus(userNameFocusNode);
      },
    );
  }

  Widget _buildUserNameInputField(
    BuildContext context,
    TextEditingController userNameController,
    FocusNode userNameFocusNode,
    FocusNode emailFocusNode,
  ) {
    return FlowkarTextFormField(
      fillColor: Theme.of(context).customColors.white,
      filled: true,
      context: context,
      // hintText: Lang.of(context).lbl_username,
      labelText: Lang.of(context).lbl_username,
      contentPadding: EdgeInsets.all(16.0),
      controller: userNameController,
      focusNode: userNameFocusNode,
      validator: AppValidations.validateUsername,
      textInputAction: TextInputAction.next,
      inputFormatters: [
        FilteringTextInputFormatter.deny(RegExp(r'\s')),
      ],
      onFieldSubmitted: (_) {
        FocusScope.of(context).requestFocus(emailFocusNode);
      },
    );
  }

  Widget _buildEmailInputField(
    BuildContext context,
    TextEditingController emailController,
    FocusNode emailFocusNode,
    FocusNode bioFocusNode,
  ) {
    return FlowkarTextFormField(
      fillColor: Theme.of(context).customColors.white,
      filled: true,
      readOnly: true,
      context: context,
      // hintText: Lang.of(context).lbl_email,
      labelText: Lang.of(context).lbl_email,
      controller: emailController,
      focusNode: emailFocusNode,
      textInputType: TextInputType.emailAddress,
      validator: AppValidations.validateEmail,
      textInputAction: TextInputAction.next,
      onFieldSubmitted: (_) {
        FocusScope.of(context).requestFocus(bioFocusNode);
      },
    );
  }

  Widget _buildBioInputField(
    BuildContext context,
    TextEditingController bioController,
    FocusNode bioFocusNode,
  ) {
    return FlowkarTextFormField(
      fillColor: Theme.of(context).customColors.white,
      filled: true,
      maxLines: 5,
      maxLength: 200,
      context: context,
      counter: Text("${bioController.text.length}/$bioMaxLength"),
      onChanged: (value) {
        setState(() {});
      },
      // hintText: Lang.of(context).lbl_bio,
      labelText: Lang.of(context).lbl_bio,
      controller: bioController,
      focusNode: bioFocusNode,
      textInputType: TextInputType.multiline,
      textInputAction: TextInputAction.newline,
    );
  }

  Widget _buildDateOfBirthInputField(
    BuildContext context,
    TextEditingController dobController,
  ) {
    return FlowkarTextFormField(
      fillColor: Theme.of(context).customColors.white,
      controller: dobController,
      filled: true,
      context: context,
      readOnly: true,
      // hintText: Lang.of(context).lbl_date_of_birth,
      labelText: Lang.of(context).lbl_date_of_birth,
      suffixIcon: CustomImageView(
        imagePath: Assets.images.icons.other.icCalendar.path,
        margin: EdgeInsets.all(16.0),
      ),
      textInputType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      onTap: () async {
        DateTime birthDate = DateTime(DateTime.now().year - 18, DateTime.now().month, DateTime.now().day);

        final DateTime? selectedDate = await showDatePicker(
          context: context,
          // initialDate: dobController.text.isNotEmpty ? DateFormat('dd/MM/yyyy').parse(dobController.text) : DateTime.now(),
          initialDate: dobController.text.isNotEmpty ? DateFormat('dd/MM/yyyy').parse(dobController.text) : birthDate,
          firstDate: DateTime(1900),
          lastDate: DateTime.now(),
          errorFormatText: "Invalid date DOB",
          errorInvalidText: "Birth date cannot be in the future",
          fieldHintText: "dd/mm/yyyy",
          locale: const Locale('en', 'GB'),
        );

        if (selectedDate != null) {
          final formattedDate = DateFormat('dd/MM/yyyy').format(selectedDate);
          dobController.text = formattedDate;
        }
      },
    );
  }

  Widget _buildMobileNumberInputField(
    BuildContext context,
    TextEditingController mobileNumberController,
    FocusNode mobileNumberFocusNode,
  ) {
    return FlowkarTextFormField(
      fillColor: Theme.of(context).customColors.white,
      filled: true,
      context: context,
      labelText: Lang.of(context).lbl_mobile_number,
      // hintText: Lang.of(context).lbl_mobile_number,
      controller: mobileNumberController,
      focusNode: mobileNumberFocusNode,
      maxLength: 10,
      textInputType: TextInputType.number,
      textInputAction: TextInputAction.next,
      onFieldSubmitted: (_) {},
    );
  }

  Widget _buildGenderDropDown(
    BuildContext context,
    FocusNode mobileNumberFocusNode,
  ) {
    return BlocBuilder<UserProfileBloc, UserProfileState>(
      builder: (context, state) {
        Map<String, String> genderNamesMap = {
          'Male': 'Male',
          'Female': 'Female',
        };

        // Ensure we're getting the correct key format from state
        final selectedGenderKey = state.selectedGender;

        // Check if selected value is valid (present in map)
        final isValidGender = selectedGenderKey.isNotEmpty && genderNamesMap.containsKey(selectedGenderKey);

        return GestureDetector(
          onTap: () {
            mobileNumberFocusNode.unfocus();
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: CustomDropdown(
            isValidGender: isValidGender,
            items: genderNamesMap.keys.toList(),
            itemNamesMap: genderNamesMap,
            hintText: Lang.of(context).lbl_gender,
            selectedValue: isValidGender ? selectedGenderKey : '',
            onChange: (String? value) {
              if (value != null) {
                context.read<UserProfileBloc>().add(UpdateGenderEvent(gender: value));
              }
            },
          ),
        );
      },
    );
  }

  _openLocationBottomSheet(BuildContext context,
      {String? title, List<DemographyItem>? items, Function(DemographyItem)? onItemsSelect}) {
    TextEditingController searchController = TextEditingController();

    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true, // Enable dragging to close

      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0.r)),
      ),
      builder: (ctx) {
        List<DemographyItem> filteredItems = List.from(items ?? []);

        return StatefulBuilder(builder: (context, setState) {
          return Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: Container(
              // Set max height to 70% of screen height
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.95,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20.0.r)),
              ),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                // Add drag handle at the top
                Container(
                  margin: EdgeInsets.symmetric(vertical: 8),
                  height: 4,
                  width: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Text(
                          title ?? "",
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w700,
                              ),
                        ),
                      ),
                      // Add close button
                      Align(
                        alignment: Alignment.centerRight,
                        child: IconButton(
                          icon: Icon(
                            Icons.close_rounded,
                            color: Theme.of(context).primaryColor,
                          ),
                          onPressed: () => Navigator.pop(ctx),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: FlowkarTextFormField(
                    context: context,
                    controller: searchController,
                    hintText: "Search...",
                    onChanged: (value) {
                      setState(() {
                        final seenNames = <String>{};
                        filteredItems = (items
                                ?.where((item) {
                                  return item.name.toLowerCase().contains(value.toLowerCase());
                                })
                                .where((item) => seenNames.add(item.name))
                                .toList()) ??
                            [];
                        for (var item in filteredItems) {
                          Logger.lOG('Matched item: ${item.name}');
                        }
                      });
                    },
                    borderDecoration: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    prefixIcon: Icon(Icons.search, color: Colors.grey),
                  ),
                ),
                SizedBox(height: 10.h),
                Divider(),
                // Use Expanded for list to take remaining space
                filteredItems.isEmpty
                    ? Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16.w, vertical: MediaQuery.of(context).size.height / 8),
                        child: ExceptionWidget(
                          imagePath: Assets.images.svg.exception.svgNodatafound.path,
                          showButton: false,
                          title: Lang.of(context).lbl_no_data_found,
                          subtitle: title == "Select State"
                              ? Lang.of(context).lbl_no_state_found
                              : Lang.of(context).lbl_no_city_found,
                        ),
                      )
                    : Expanded(
                        child: ListView.builder(
                            itemCount: filteredItems.length,
                            itemBuilder: (context, index) {
                              final item = filteredItems[index];
                              return ListTile(
                                title: Text(item.name),
                                onTap: () {
                                  if (onItemsSelect != null) {
                                    onItemsSelect(item);
                                  }
                                  Navigator.pop(ctx);
                                },
                              );
                            }),
                      ),
              ]),
            ),
          );
        });
      },
    );
  }

  Widget _buildCountrySelect(BuildContext context) {
    return BlocBuilder<UserProfileBloc, UserProfileState>(
      builder: (context, state) {
        return FlowkarTextFormField(
          context: context,
          readOnly: true,
          controller: state.countryController,
          labelText: "Country",
          hintText: "Select Country",
          suffixIcon: CustomImageView(
            imagePath: Assets.images.icons.other.icDropdown.path,
            margin: EdgeInsets.all(13.w),
          ),
          onTap: () {
            _openLocationBottomSheet(
              context,
              title: "Select Country",
              items: state.countries,
              onItemsSelect: (selected) {
                context.read<UserProfileBloc>().add(SelectCountryEvent(selected));
              },
            );
          },
        );
      },
    );
  }

  Widget _buildStateSelect(BuildContext context) {
    return BlocBuilder<UserProfileBloc, UserProfileState>(
      builder: (context, state) {
        return FlowkarTextFormField(
          key: stateFieldKey,
          context: context,
          readOnly: true,
          controller: state.stateController,
          labelText: "State",
          hintText: "Select State",
          suffixIcon: state.isLoadingStates || state.loading
              ? SizedBox(height: 22.h, width: 22.h, child: LoadingAnimationWidget())
              : CustomImageView(
                  imagePath: Assets.images.icons.other.icDropdown.path,
                  margin: EdgeInsets.all(13.w),
                ),
          validator: (value) {
            // Validate state selection only if country is selected
            if (state.countryController!.text.isNotEmpty) {
              if (value == null || value.isEmpty) {
                return 'Please select a State';
              }
            }
            return null;
          },
          onTap: state.isLoadingStates || state.loading
              ? () {}
              : () {
                  if (state.countryController?.text.isEmpty ?? true) {
                    toastification.show(
                      type: ToastificationType.error,
                      showProgressBar: false,
                      title: Text(
                        "Please select Country first",
                        style: GoogleFonts.montserrat(
                          fontSize: 12.0.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      autoCloseDuration: const Duration(seconds: 3),
                    );
                  } else {
                    _openLocationBottomSheet(
                      context,
                      title: "Select State",
                      items: state.states,
                      onItemsSelect: (selected) {
                        // Reset city when state changes
                        context.read<UserProfileBloc>().add(
                              SelectStateEvent(selected),
                            );
                      },
                    );
                  }
                },
        );
      },
    );
  }

  Widget _buildCitySelect(BuildContext context) {
    return BlocBuilder<UserProfileBloc, UserProfileState>(
      builder: (context, state) {
        return FlowkarTextFormField(
          key: cityFieldKey,
          context: context,
          readOnly: true,
          controller: state.cityController,
          labelText: "City",
          hintText: "Select City",
          suffixIcon: state.isLoadingCities || state.loading
              ? SizedBox(height: 22.h, width: 22.h, child: LoadingAnimationWidget())
              : CustomImageView(
                  imagePath: Assets.images.icons.other.icDropdown.path,
                  margin: EdgeInsets.all(13.w),
                ),
          validator: (value) {
            // Validate city selection only if country and state are selected
            if (state.countryController!.text.isNotEmpty || state.stateController!.text.isNotEmpty) {
              if (value == null || value.isEmpty) {
                return 'Please select a City';
              }
            }
            return null;
          },
          onTap: state.isLoadingCities || state.loading
              ? () {}
              : () {
                  // Ensure country and state are selected before opening city selection
                  if (state.countryController!.text.isEmpty) {
                    toastification.show(
                      type: ToastificationType.error,
                      showProgressBar: false,
                      title: Text(
                        "Please select Country first",
                        style: GoogleFonts.montserrat(
                          fontSize: 12.0.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      autoCloseDuration: const Duration(seconds: 3),
                    );
                  } else if (state.stateController!.text.isEmpty) {
                    toastification.show(
                      type: ToastificationType.error,
                      showProgressBar: false,
                      title: Text(
                        "Please select State first",
                        style: GoogleFonts.montserrat(
                          fontSize: 12.0.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      autoCloseDuration: const Duration(seconds: 3),
                    );
                  } else {
                    _openLocationBottomSheet(
                      context,
                      title: "Select City",
                      items: state.cities,
                      onItemsSelect: (selected) {
                        context.read<UserProfileBloc>().add(SelectCityEvent(selected));
                      },
                    );
                  }
                },
        );
      },
    );
  }

  Future<void> _cropImage(String imagePath) async {
    setState(() {
      isImageLoading = true;
    });

    try {
      final croppedFile = await showDialog(
        context: context,
        builder: (context) => Dialog(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Edit Photo',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              Divider(),
              SizedBox(
                height: 400,
                width: 400,
                child: ClipRRect(
                  child: CustomImageCrop(
                    cropController: cropController,
                    image: FileImage(File(imagePath)),
                    shape: CustomCropShape.Circle,
                    cropPercentage: 0.8,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('Cancel'),
                    ),
                    CustomElevatedButton(
                      width: 150,
                      text: 'Crop',
                      onPressed: () async {
                        setState(() {
                          isLoading = true;
                        });

                        NavigatorService.goBack();

                        log("isLoading: $isLoading");
                        final image = await cropController.onCropImage();
                        if (image != null) {
                          final tempDir = await getTemporaryDirectory();
                          final String uniqueId = Uuid().v4();
                          final String tempPath = '${tempDir.path}/cropped_image_$uniqueId.jpg';

                          final file = File(tempPath);
                          await file.writeAsBytes(image.bytes);

                          profileFilePath = file;

                          Future.delayed(Duration(seconds: 2), () {
                            setState(() {
                              isLoading = false;
                            });

                            log("isLoading to: $isLoading");
                          });
                        }
                      },
                      isLoading: isLoading,
                      isDisabled: isLoading,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );

      if (croppedFile != null) {
        setState(() {
          profileFilePath = croppedFile;
        });
      }
    } finally {
      setState(() {
        isImageLoading = false;
      });
    }
  }
}
