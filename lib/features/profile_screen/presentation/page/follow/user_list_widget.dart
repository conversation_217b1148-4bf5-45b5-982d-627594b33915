import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/profile_screen/model/get_follower_list_model.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';

class UserListWidget extends StatefulWidget {
  final TextEditingController searchController;
  final bool showSearchField;
  final List<FolllowData>? followList;
  final ScrollController scrollController;
  final String? type;
  final bool? isVisitor;

  const UserListWidget({
    super.key,
    required this.searchController,
    required this.showSearchField,
    required this.scrollController,
    this.followList,
    this.type,
    this.isVisitor,
  });

  @override
  State<UserListWidget> createState() => _UserListWidgetState();
}

class _UserListWidgetState extends State<UserListWidget> {
  bool showCloseButton = false;

  @override
  void initState() {
    super.initState();
    widget.searchController.addListener(_updateCloseButtonVisibility);
  }

  void _updateCloseButtonVisibility() {
    setState(() {
      showCloseButton = widget.searchController.text.isNotEmpty;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return Column(
          children: [
            Expanded(
              child: Column(
                children: [
                  if (widget.showSearchField) _buildSearchTextField(themestate),
                  if (widget.showSearchField) buildSizedBoxH(8.0),
                  Expanded(
                    child: _buildUserList(themestate, widget.followList!),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUserList(ThemeState themestate, List<FolllowData> followList) {
    return
        // ShowUpTransition(
        // forward: true,
        // delay: const Duration(milliseconds: 300),
        // child:
        ListView.builder(
      controller: widget.scrollController,
      padding: EdgeInsets.zero,
      physics: AlwaysScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: widget.followList?.length,
      itemBuilder: (context, index) {
        return _buildUserDetail(context, themestate, index);
      },
      // ),
    );
  }

  Widget _buildUserDetail(BuildContext context, ThemeState themestate, int index) {
    final userId = widget.followList?[index].id; // Convert to String
    final currentUserId = Prefobj.preferences?.get(Prefkeys.USER_ID)?.toString(); // Convert to String
    final isCurrentUser = userId.toString() == currentUserId;

    Logger.lOG("isCurrentUser: $isCurrentUser, userId: $userId, currentUserId: $currentUserId");

    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 50.0.h,
                width: 50.0.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.r),
                  border: Border.all(
                    color: Theme.of(context).primaryColor,
                    width: 2.w,
                  ),
                ),
                child: ClipRRect(
                  clipBehavior: Clip.hardEdge,
                  child: Padding(
                    padding: EdgeInsets.all(widget.followList?[index].profilePicture == null ? 12.0 : 2.0),
                    child: CustomImageView(
                      radius: BorderRadius.circular(widget.followList?[index].profilePicture == null ? 0 : 100.r),
                      height: 50.0.h,
                      width: 50.0.w,
                      fit: widget.followList?[index].profilePicture == null ? BoxFit.contain : BoxFit.cover,
                      imagePath: widget.followList?[index].profilePicture == null
                          ? AssetConstants.pngUser
                          : widget.followList?[index].profilePicture ?? '',
                      alignment: Alignment.center,
                    ),
                  ),
                ),
              ),
              buildSizedBoxW(8),
              InkWell(
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: GetUserProfileById(userId: userId, stackonScreen: true));
                },
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.followList?[index].name ?? '',
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontSize: 12.sp, fontWeight: FontWeight.w700),
                    ),
                    Text(
                      "@${widget.followList?[index].username ?? ''}",
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontSize: 10.sp,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (!isCurrentUser)
            InkWell(
              onTap: () {
                if (widget.type == Lang.of(context).lbl_likes) {
                  context
                      .read<HomeFeedBloc>()
                      .add(FollowUserLikeListSocketEvent(userId: widget.followList?[index].id ?? 0));
                } else {
                  if (widget.isVisitor == true) {
                    context.read<UserProfileBloc>().add(FollowUserProfilebyIdSocketEvent(
                        type: widget.type ?? '', userId: widget.followList?[index].id ?? 0));
                  } else {
                    context.read<UserProfileBloc>().add(FollowUserProfileSocketEvent(
                        type: widget.type ?? '', userId: widget.followList?[index].id ?? 0));
                  }
                }
              },
              child: !widget.followList![index].isFollowing!
                  ? Container(
                      height: 35.0.h,
                      width: MediaQuery.of(context).size.width * 0.3,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.2)),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.0.w),
                        child: Text(
                          Lang.of(context).lbl_follow,
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium!
                              .copyWith(fontSize: 12.sp, color: Theme.of(context).customColors.white),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    )
                  : Container(
                      height: 35.0.h,
                      width: MediaQuery.of(context).size.width * 0.3,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.2)),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.0.w),
                        child: Text(
                          Lang.of(context).lbl_following,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 12.sp),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchTextField(ThemeState themestate) {
    return FlowkarTextFormField(
      height: 48.0.h,
      context: context,
      controller: widget.searchController,
      // fillColor: AppColors.searchtextfieldcolor,
      filled: true,
      isLabelEnabled: false,
      labelText: Lang.of(context).lbl_search,
      // prefix: CustomImageView(
      //   // color: themestate.isDarkThemeOn ? AppColors.whitecolor : null,
      //   imagePath: Assets.images.icons.icSearch.path,
      //   margin: const EdgeInsets.all(16.0),
      //   height: 16.0.h,
      //   width: 16.0.w,
      // ),
      // onChanged: (value) {
      //   // context
      //   //     .read<UserProfileBloc>()
      //   //     .add(SearchFollowListEvent(searchtext: value));
      // },
      // suffix: Visibility(
      //   visible: showCloseButton,
      //   child: IconButton(
      //     icon: CustomImageView(
      //       // color: themestate.isDarkThemeOn ? AppColors.whitecolor : null,
      //       height: 16.0.h,
      //       width: 16.0.w,
      //       imagePath: Assets.images.icons.icClose.path,
      //     ),
      //     onPressed: () {
      //       widget.searchController.clear();
      //       FocusScope.of(context).unfocus();
      //     },
      //   ),
      // ),
      inputAction: TextInputAction.done,
    );
  }
}
