import 'dart:async';
import 'dart:io';

import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/services/multi_account_manager.dart';
import 'package:flowkar/features/profile_screen/model/demography_response_model.dart';
import 'package:flowkar/features/profile_screen/model/get_follower_list_model.dart';
import 'package:flowkar/features/profile_screen/model/qr_generate_model.dart';
import 'package:flowkar/features/profile_screen/model/user_profile_detail_model.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/models/story_model.dart';

part 'profile_event.dart';
part 'profile_state.dart';

class UserProfileBloc extends Bloc<UserProfileEvent, UserProfileState> {
  final ApiClient apiClient = ApiClient(Dio());

  UserProfileBloc() : super(UserProfileState.initial()) {
    on<UserProfileInitial>(_onInitialize);
    on<FetchShareProfileQrEvent>(_onFetchShareQrProfile);
    on<UpdateGenderEvent>(_onUpdateGender);
    on<RemoveImageFromProfile>(_removeImageFromProfile);
    on<EditUserProfileEvent>(_onEditUserProfile);
    on<FetchUserProfileEvent>(_onFetchUserProfile);
    on<GetHighlightStoryApiEvent>(getHilight);
    // on<GetTagPostApi>(_getTagPostApi);
    on<GetFollowerListApiEvent>(_getFollowerListApi);
    on<FollowUserProfilebyIdSocketEvent>(_followUserProfileSocket);
    on<FollowUserProfileSocketEvent>(_followUserProfileSocketEvent);
    on<GetFollowerListbyIdApiEvent>(_getFollowerListByIdApi);
    on<GetFollowingListApiEvent>(_getFollowingListApi);
    on<GetFollowingListbyIdApiEvent>(_getFollowingListByIdApi);
    on<DeleteHighLightStoryEvent>(_onDeleteHighLightStoryEvent);
    // on<FollowUserSocketEvent>(_followUserSocket);

    on<LoadCountriesEvent>(_onLoadCountries);
    on<LoadStatesEvent>(_onLoadStates);
    on<LoadCitiesEvent>(_onLoadCities);
    on<SelectCountryEvent>(_onSelectCountry);
    on<SelectStateEvent>(_onSelectState);
    on<SelectCityEvent>(_onSelectCity);
  }

  FutureOr<void> _onInitialize(UserProfileInitial event, Emitter<UserProfileState> emit) {
    emit(state.copyWith(
        loading: false,
        error: null,
        searchController: TextEditingController(),
        followList: [],
        followingList: [],
        isloding: false,
        editloading: false,
        cityController: TextEditingController(),
        countryController: TextEditingController(),
        bioController: TextEditingController(),
        nameController: TextEditingController(),
        emailController: TextEditingController(),
        stateController: TextEditingController(),
        highlightStoryData: []));
  }

  Future<void> _onFetchUserProfile(FetchUserProfileEvent event, Emitter<UserProfileState> emit) async {
    // if (state.loading) return;

    emit(state.copyWith(loading: true, error: null));

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final response = await apiClient.getUserProfileDetail(logInUserId: loginuserId, brandid: brandId.toString());

      if (response.status) {
        final userProfile = response;

        brandNameNotifier.value = userProfile.data.username;
        profileImageNotifier.value =
            userProfile.data.profileImage.isEmpty ? AssetConstants.pngUser : userProfile.data.profileImage;

        // Update stored account data in MultiAccountManager
        try {
          await MultiAccountManager.updateCurrentAccountData(
            name: userProfile.data.name.toString(),
            username: userProfile.data.username.toString(),
            profileImage: userProfile.data.profileImage.toString(),
          );
          Logger.lOG("Updated stored account data after profile edit");
        } catch (error) {
          Logger.lOG("Error updating stored account data: $error");
        }

        if (event.onEdit == true) {
          brandNameNotifier.value = userProfile.data.username;
          final countriesResponse = await apiClient.getDemographyList(loginuserId, brandId.toString(), 1, null);
          final countries = countriesResponse.data;

          final userCountry = countries.firstWhere(
            (c) => c.name == userProfile.data.country,
            orElse: () => DemographyItem(name: "", forwardingId: 0, requestType: 0),
          );

          List<DemographyItem> states = [];
          if (userCountry.forwardingId > 0) {
            final statesResponse =
                await apiClient.getDemographyList(loginuserId, brandId.toString(), 2, userCountry.forwardingId);
            states = statesResponse.data;
          }

          final userState = states.firstWhere(
            (s) => s.name == userProfile.data.state,
            orElse: () => DemographyItem(name: "", forwardingId: 0, requestType: 0),
          );

          List<DemographyItem> cities = [];
          if (userState.forwardingId > 0) {
            final citiesResponse =
                await apiClient.getDemographyList(loginuserId, brandId.toString(), 3, userState.forwardingId);
            cities = citiesResponse.data;
          }
          await Prefobj.preferences?.put(Prefkeys.NAME, userProfile.data.name.toString());
          String? image = response.data.profileImage.isEmpty ? AssetConstants.pngUser : response.data.profileImage;
          await Prefobj.preferences?.put(Prefkeys.PROFILE, image);
          profileImageNotifier.value = image;
          await Prefobj.preferences?.put(Prefkeys.USERNAME, userProfile.data.username.toString());

          emit(state.copyWith(
            loading: false,
            userProfile: userProfile,
            error: null,
            selectedGender: userProfile.data.gender,
            profile: userProfile.data.profileImage.isNotEmpty ? userProfile.data.profileImage : '',
            countries: countries,
            states: states,
            cities: cities,
            selectedCountry: userCountry,
            selectedState: userState,
            selectedCity: cities.firstWhere(
              (c) => c.name == userProfile.data.city,
              orElse: () => DemographyItem(name: "", forwardingId: 0, requestType: 0),
            ),
            nameController: TextEditingController(text: userProfile.data.name),
            userNameController: TextEditingController(text: userProfile.data.username),
            emailController: TextEditingController(text: userProfile.data.email),
            bioController: TextEditingController(text: userProfile.data.bio),
            dobController: TextEditingController(text: userProfile.data.dob),
            mobileNumberController: TextEditingController(text: userProfile.data.mobile),
            countryController: TextEditingController(text: userProfile.data.country),
            stateController: TextEditingController(text: userProfile.data.state),
            cityController: TextEditingController(text: userProfile.data.city),
          ));
        } else {
          emit(state.copyWith(
            loading: false,
            userProfile: userProfile,
            error: null,
            selectedGender: userProfile.data.gender,
            profile: userProfile.data.profileImage.isNotEmpty ? userProfile.data.profileImage : '',
            nameController: TextEditingController(text: userProfile.data.name),
            userNameController: TextEditingController(text: userProfile.data.username),
            emailController: TextEditingController(text: userProfile.data.email),
            bioController: TextEditingController(text: userProfile.data.bio),
            dobController: TextEditingController(text: userProfile.data.dob),
            mobileNumberController: TextEditingController(text: userProfile.data.mobile),
            cityController: TextEditingController(text: userProfile.data.city),
            countryController: TextEditingController(text: userProfile.data.country),
            stateController: TextEditingController(text: userProfile.data.state),
          ));
        }
      } else {
        emit(state.copyWith(loading: false, error: "Failed to fetch profile"));
      }
    } catch (e) {
      emit(state.copyWith(loading: false, error: e.toString()));
    }
  }

  Future<void> _onFetchShareQrProfile(FetchShareProfileQrEvent event, Emitter<UserProfileState> emit) async {
    emit(state.copyWith(loading: true, error: null));

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final response = await apiClient.getShareProfileQr(logInUserId: loginuserId, brandid: brandId.toString());

      if (response.status) {
        emit(state.copyWith(
          loading: false,
          qrResponseModel: response,
          error: null,
        ));
      } else {
        emit(state.copyWith(
          loading: false,
          error: response.message,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        loading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> _removeImageFromProfile(RemoveImageFromProfile event, Emitter<UserProfileState> emit) async {
    emit(state.copyWith(profile: ''));
  }

  Future<void> _onEditUserProfile(EditUserProfileEvent event, Emitter<UserProfileState> emit) async {
    Logger.lOG("Edit page ${event.profileImage}");
    emit(state.copyWith(editloading: true, error: null));

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      Logger.lOG("Edit page ${event.profileImage}");
      final response = await apiClient.editUserProfile(
          logInUserId: loginuserId,
          profileImage: event.profileImage,
          name: event.name,
          username: event.username,
          bio: event.bio,
          dob: event.dob,
          mobile: event.mobile,
          gender: event.gender,
          city: state.selectedCity?.name,
          state: state.selectedState?.name,
          country: state.selectedCountry?.name,
          brandid: brandId.toString());

      if (response.status == true) {
        String? image = response.profileImage?.isEmpty ?? true ? AssetConstants.pngUser : response.profileImage;
        Prefobj.preferences?.put(Prefkeys.PROFILE, image);
        profileImageNotifier.value = image ?? "";
        await Prefobj.preferences?.put(Prefkeys.NAME, event.name.toString());
        await Prefobj.preferences?.put(Prefkeys.USERNAME, event.username.toString());

        // Update stored account data in MultiAccountManager
        try {
          await MultiAccountManager.updateCurrentAccountData(
            name: event.name,
            username: event.username,
            profileImage: image,
          );
          Logger.lOG("Updated stored account data after profile edit");
        } catch (error) {
          Logger.lOG("Error updating stored account data: $error");
        }

        Logger.lOG("Response page ${response.profileImage}");
        await _onFetchUserProfile(FetchUserProfileEvent(onEdit: false), emit);

        emit(state.copyWith(editloading: false, profile: response.profileImage, error: ''));
      } else {
        emit(state.copyWith(editloading: false, error: response.message));
      }
    } catch (e) {
      debugPrint('Error updating profile: $e');

      emit(state.copyWith(editloading: false, error: 'Failed to update profile. Please try again.'));
    }
  }
  // Future<void> _onEditUserProfile(EditUserProfileEvent event, Emitter<UserProfileState> emit) async {
  //   Logger.lOG("Edit page ${event.profileImage}");
  //   if (state.loading) return;

  //   emit(state.copyWith(loading: true, error: null));

  //   try {
  //     String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
  //     int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

  //     // Explicitly handle null profile image
  //     final response = await apiClient.editUserProfile(
  //       logInUserId: loginuserId,
  //       profileImage: event.profileImage, // Pass null directly if image is removed
  //       name: event.name,
  //       username: event.username,
  //       bio: event.bio,
  //       dob: event.dob,
  //       mobile: event.mobile,
  //       gender: event.gender,
  //       city: state.selectedCity?.name,
  //       state: state.selectedState?.name,
  //       country: state.selectedCountry?.name,
  //       brandid: brandId.toString(),
  //     );

  //     if (response.status == true) {
  //       // Handle empty profile image case
  //       String? image = response.profileImage?.isEmpty ?? true ? AssetConstants.pngUser : response.profileImage;

  //       Prefobj.preferences?.put(Prefkeys.PROFILE, image);
  //       profileImageNotifier.value = image ?? "";
  //       await Prefobj.preferences?.put(Prefkeys.NAME, event.name.toString());

  //       await _onFetchUserProfile(FetchUserProfileEvent(onEdit: false), emit);
  //       emit(state.copyWith(
  //         loading: false,
  //         profile: response.profileImage, // Ensure empty string if null
  //         error: '',
  //       ));
  //     } else {
  //       emit(state.copyWith(
  //         loading: false,
  //         error: response.message,
  //       ));
  //     }
  //   } catch (e) {
  //     debugPrint('Error updating profile: $e');
  //     emit(state.copyWith(
  //       loading: false,
  //       editloading: false,
  //       error: 'Failed to update profile. Please try again.',
  //     ));
  //   }
  // }

  Future<void> _onUpdateGender(UpdateGenderEvent event, Emitter<UserProfileState> emit) async {
    emit(state.copyWith(selectedGender: event.gender));
  }

  // Future<void> getHilight(GetHighlightStoryApiEvent event, Emitter<UserProfileState> emit) async {
  //   try {
  //     emit(state.copyWith(ishilightloading: true));
  //     final highlightstoryResponse = await apiClient.getHighlight();
  //     if (highlightstoryResponse.status == true) {
  //       // state.highlightStoryData.clear();
  //       state.highlightStoryData.addAll(highlightstoryResponse.data ?? []);
  //       Logger.lOG("TOTAL Highlight STORY LENGTH : ${state.highlightStoryData.length}");
  //       emit(state.copyWith(
  //         highlightStoryModel: highlightstoryResponse,
  //         highlightStoryData: state.highlightStoryData,
  //         ishilightloading: false,
  //       ));
  //     } else {
  //       emit(state.copyWith(ishilightloading: false));
  //     }
  //   } catch (e) {
  //     emit(state.copyWith(
  //       ishilightloading: false,
  //     ));

  //     Logger.lOG(e.toString());
  //   }
  // }
  Future<void> getHilight(GetHighlightStoryApiEvent event, Emitter<UserProfileState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      emit(state.copyWith(ishilightloading: true));

      final highlightstoryResponse =
          await apiClient.getHighlight(logInUserId: loginuserId, brandid: brandId.toString());

      if (highlightstoryResponse.status == true) {
        // Mutable copy of the existing list
        final List<NewStory> existingList = [...state.highlightStoryData];

        // Gather all existing storyIds
        final Set<int> existingStoryIds = {
          for (var storyGroup in existingList) ...?storyGroup.stories?.map((e) => e.storyId).whereType<int>()
        };

        final List<NewStory> newList = highlightstoryResponse.data ?? [];
        final List<NewStory> uniqueNewStories = [];

        for (final newStoryGroup in newList) {
          final List<Stories> filteredStories = (newStoryGroup.stories ?? [])
              .where(
                (s) => s.storyId != null && !existingStoryIds.contains(s.storyId),
              )
              .toList();

          if (filteredStories.isNotEmpty) {
            // Add new storyIds to the set to prevent future duplicates
            existingStoryIds.addAll(filteredStories.map((e) => e.storyId!));

            // Add the NewStory object with filtered stories
            uniqueNewStories.add(
              newStoryGroup.copyWith(stories: filteredStories),
            );
          }
        }

        // Add all unique NewStory groups to the existing list
        existingList.addAll(uniqueNewStories);

        Logger.lOG("TOTAL Highlight STORY LENGTH : ${existingList.length}");

        emit(state.copyWith(
          highlightStoryModel: highlightstoryResponse,
          highlightStoryData: existingList,
          ishilightloading: false,
        ));
      } else {
        emit(state.copyWith(ishilightloading: false));
      }
    } catch (e) {
      emit(state.copyWith(ishilightloading: false));
      Logger.lOG(e.toString());
    }
  }

  _getFollowerListApi(GetFollowerListApiEvent event, Emitter<UserProfileState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      if (event.page == 1) {
        emit(state.copyWith(isloding: true, isLoadingMore: false));
      } else {
        emit(state.copyWith(isLoadingMore: true));
      }

      // API Call
      final getFollowerListModel = await apiClient.getfollowListApi(loginuserId, event.page, brandId.toString());

      if (getFollowerListModel.results?.status == true) {
        // Create a new mutable list
        // final List<FolllowData> updatedFollowList = [
        //   if (event.page != 1) ...state.followList, // Preserve previous data if not first page
        //   ...?getFollowerListModel.results?.data, // Add new data safely
        // ];
        // Existing IDs to avoid duplicates
        final existingIds = state.followList.map((e) => e.id).toSet();

        // Filter out duplicates
        final newItems =
            (getFollowerListModel.results?.data ?? []).where((item) => !existingIds.contains(item.id)).toList();

        final updatedFollowList = List.of(state.followList)..addAll(newItems);

        Logger.lOG("TOTAL FOLLOW LIST LENGTH : ${updatedFollowList.length}");

        emit(state.copyWith(
          page: event.page,
          isloding: false,
          followList: updatedFollowList, // Assigning new list
          isLoadingMore: false,
        ));
        // Auto-load next page if available and this is page 1
        if (event.page == 1 && getFollowerListModel.next != null) {
          add(GetFollowerListApiEvent(page: 2));
        }
      }
    } catch (e) {
      Logger.lOG("Error in _getFollowerListApi: $e");

      emit(state.copyWith(isLoadingMore: false, isloding: false));
    }
  }

  _followUserProfileSocket(FollowUserProfilebyIdSocketEvent event, Emitter<UserProfileState> emit) async {
    try {
      final completer = Completer<Map<String, dynamic>>();

      // Emit the event to the socket
      SocketService.emit(APIConfig.followuser, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'to_user_id': event.userId,
      });
      bool isCompleted = false;
      // Listen for the response from the socket
      SocketService.response(
        APIConfig.followuser,
        (response) {
          if (!isCompleted) {
            // Complete the completer with the response
            completer.complete(response);
            isCompleted = true; // Set isCompleted to true to avoid multiple completions
          }
        },
      );

      // Await the response
      final response = await completer.future;
      final followed = response['followed'];
      final followingCount = response['following'];

      _onUpdatefollowUserProfileSocket(
          UpdateFollowUserProfilebyIdEvent(
              isFollowing: followed, userId: event.userId, type: event.type, followingcount: followingCount),
          emit);
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onUpdatefollowUserProfileSocket(UpdateFollowUserProfilebyIdEvent event, Emitter<UserProfileState> emit) {
    try {
      // Iterate through the current follow list and update the relevant user's isFollowing status
      if (event.type == "Follow") {
        final updatedFollowList = state.followList.map((follower) {
          if (follower.id == event.userId) {
            // Return a new instance of the follower with the updated isFollowing status
            return follower.copyWith(isFollowing: event.isFollowing);
          }
          return follower;
        }).toList();

        // Emit the new state with the updated followList
        emit(state.copyWith(followList: updatedFollowList));
      } else {
        final updatedFollowingList = state.followingList.map((follower) {
          if (follower.id == event.userId) {
            // Return a new instance of the follower with the updated isFollowing status
            return follower.copyWith(isFollowing: event.isFollowing);
          }
          return follower;
        }).toList();

        // Emit the new state with the updated followList
        emit(state.copyWith(
            followingList: updatedFollowingList,
            userProfile: state.userProfile
                ?.copyWith(data: state.userProfile?.data.copyWith(numberOfFollowing: event.followingcount))));
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _followUserProfileSocketEvent(FollowUserProfileSocketEvent event, Emitter<UserProfileState> emit) async {
    try {
      final completer = Completer<Map<String, dynamic>>();

      SocketService.emit(APIConfig.followuser, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'to_user_id': event.userId,
      });
      bool isCompleted = false;
      SocketService.response(
        APIConfig.followuser,
        (response) {
          if (!isCompleted) {
            completer.complete(response);
            isCompleted = true;
          }
        },
      );

      final response = await completer.future;
      final followed = response['followed'];
      final followingCount = response['following'];

      _onUpdateFollowUserProfileEvent(
          UpdateFollowUserProfileEvent(
              isFollowing: followed, userId: event.userId, type: event.type, followingcount: followingCount),
          emit);
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onUpdateFollowUserProfileEvent(UpdateFollowUserProfileEvent event, Emitter<UserProfileState> emit) {
    try {
      if (event.type == "Follow") {
        final updatedFollowList = state.followList.map((follower) {
          if (follower.id == event.userId) {
            return follower.copyWith(isFollowing: event.isFollowing);
          }
          return follower;
        }).toList();

        emit(state.copyWith(
            followList: updatedFollowList,
            userProfile: state.userProfile?.copyWith(
                data: state.userProfile?.data.copyWith(
              numberOfFollowing: event.followingcount,
            ))));
      } else {
        final updatedFollowingList = state.followingList.map((follower) {
          if (follower.id == event.userId) {
            return follower.copyWith(isFollowing: event.isFollowing);
          }
          return follower;
        }).toList();

        emit(state.copyWith(
            followingList: updatedFollowingList,
            userProfile: state.userProfile
                ?.copyWith(data: state.userProfile?.data.copyWith(numberOfFollowing: event.followingcount))));
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _getFollowerListByIdApi(GetFollowerListbyIdApiEvent event, Emitter<UserProfileState> emit) async {
    try {
      if (event.page == 1) {
        emit(state.copyWith(isloding: true, isLoadingMore: false));
      } else {
        emit(state.copyWith(isLoadingMore: true));
      }
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final getFollowerListModel =
          await apiClient.getfollowListbyIdApi(loginuserId, event.page, event.userId, brandId.toString());

      if (getFollowerListModel.results?.status == true) {
        // final updatedFollowList = List.of(state.followList)..addAll(getFollowerListModel.results?.data ?? []);
        // ✅ Step 1: Extract existing IDs
        final existingIds = state.followList.map((e) => e.id).toSet(); // Replace `.id` with correct unique field

        // ✅ Step 2: Filter only new, unique entries
        final newItems = (getFollowerListModel.results?.data ?? [])
            .where((item) => !existingIds.contains(item.id)) // Use appropriate ID field
            .toList();

        // ✅ Step 3: Combine both
        final updatedFollowList = List.of(state.followList)..addAll(newItems);

        Logger.lOG("TOTAL FOLLOW LIST LENGTH : ${updatedFollowList.length}");

        emit(state.copyWith(
          page: event.page,
          isloding: false,
          followList: updatedFollowList,
          isLoadingMore: false,
        ));
        // Auto-load next page if available and this is page 1
        if (event.page == 1 && getFollowerListModel.next != null) {
          add(GetFollowerListbyIdApiEvent(page: 2, userId: event.userId));
        }
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isLoadingMore: false, isloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isloding: false));
      });
    }
  }

  _getFollowingListApi(GetFollowingListApiEvent event, Emitter<UserProfileState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      if (event.page == 1) {
        emit(state.copyWith(isloding: true, isLoadingMore: false));
      } else {
        emit(state.copyWith(isLoadingMore: true));
      }

      final getFollowerListModel = await apiClient.getfollowingListApi(loginuserId, event.page, brandId.toString());

      if (getFollowerListModel.results?.status == true) {
        // final updatedFollowingList = List.of(state.followingList)..addAll(getFollowerListModel.results?.data ?? []);
        // Get existing IDs
        final existingIds = state.followingList.map((e) => e.id).toSet();

        // Filter out any items already in the list
        final newItems =
            (getFollowerListModel.results?.data ?? []).where((item) => !existingIds.contains(item.id)).toList();

        // Append filtered new items to existing list
        final updatedFollowingList = List.of(state.followingList)..addAll(newItems);

        Logger.lOG("TOTAL FOLLOWING LIST LENGTH : ${updatedFollowingList.length}");
        emit(state.copyWith(
          page: event.page,
          isloding: false,
          followingList: updatedFollowingList,
          isLoadingMore: false,
        ));
        // Auto-load next page if available and this is page 1
        if (event.page == 1 && getFollowerListModel.next != null) {
          add(GetFollowingListApiEvent(page: 2));
        }
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isLoadingMore: false, isloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isloding: false));
      });
    }
  }

  // _getFollowingListByIdApi(GetFollowingListbyIdApiEvent event, Emitter<UserProfileState> emit) async {
  //   try {
  //     if (event.page == 1) {
  //       emit(state.copyWith(isloding: true, isLoadingMore: false));
  //     } else {
  //       emit(state.copyWith(isLoadingMore: true));
  //     }
  //     final getFollowerListModel = await apiClient.getfollowingListbyIdApi(event.page, event.userId);
  //     if (getFollowerListModel.results?.status == true) {
  //       state.followingList.addAll(getFollowerListModel.results?.data ?? []);
  //       Logger.lOG("TOTAL FOLLOWING LIST LENGTH : ${state.followingList.length}");
  //       emit(state.copyWith(page: event.page, isloding: false, followingList: state.followingList, isLoadingMore: false));
  //     }
  //   } catch (e) {
  //     await Future.delayed(const Duration(seconds: 1), () {
  //       emit(state.copyWith(isLoadingMore: false, isloding: false));
  //     });
  //     await Future.delayed(const Duration(seconds: 1), () {
  //       emit(state.copyWith(isloding: false));
  //     });
  //   }
  // }

  _getFollowingListByIdApi(GetFollowingListbyIdApiEvent event, Emitter<UserProfileState> emit) async {
    try {
      if (event.page == 1) {
        emit(state.copyWith(isloding: true, isLoadingMore: false));
      } else {
        emit(state.copyWith(isLoadingMore: true));
      }
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final getFollowingListModel =
          await apiClient.getfollowingListbyIdApi(loginuserId, event.page, event.userId, brandId.toString());

      if (getFollowingListModel.results?.status == true) {
        // final newList = List.of(state.followingList)..addAll(getFollowingListModel.results?.data ?? []);
        // Existing IDs to avoid duplicates
        final existingIds = state.followingList.map((e) => e.id).toSet();

        // Filter out duplicates based on ID
        final newItems =
            (getFollowingListModel.results?.data ?? []).where((item) => !existingIds.contains(item.id)).toList();

        final newList = List.of(state.followingList)..addAll(newItems);
        Logger.lOG("TOTAL FOLLOWING LIST LENGTH : ${newList.length}");
        emit(state.copyWith(
          page: event.page,
          isloding: false,
          followingList: newList,
          isLoadingMore: false,
        ));
        // Auto-load next page if available and this is page 1
        if (event.page == 1 && getFollowingListModel.next != null) {
          add(GetFollowingListbyIdApiEvent(page: 2, userId: event.userId));
        }
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isLoadingMore: false, isloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isloding: false));
      });
    }
  }

  Future<void> _onLoadCountries(
    LoadCountriesEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final response = await apiClient.getDemographyList(loginuserId, brandId.toString(), 1, null);
      if (response.status) {
        emit(state.copyWith(
          states: [],
          cities: [],
        ));
        emit(state.copyWith(countries: response.data));

        // Select user's country if profile is loaded
        if (state.userProfile != null) {
          final userCountry = response.data.firstWhere(
            (c) => c.name == state.userProfile!.data.country,
            // orElse: () => null,
          );
          final userState = response.data.firstWhere(
            (c) => c.name == state.userProfile!.data.state,
            // orElse: () => null,
          );
          final userCity = response.data.firstWhere(
            (c) => c.name == state.userProfile!.data.city,
            // orElse: () => null,
          );
          if (userCountry.name.isNotEmpty && userState.name.isNotEmpty && userCity.name.isNotEmpty) {
            emit(state.copyWith(
              selectedCountry: userCountry,
              selectedCity: userCity,
              selectedState: userState,
            ));
          }

          // if (userCountry != null) add(SelectCountryEvent(userCountry));
        }
      }
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _onLoadStates(
    LoadStatesEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    emit(state.copyWith(isLoadingStates: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final response = await apiClient.getDemographyList(loginuserId, brandId.toString(), 2, event.countryId);
      emit(state.copyWith(states: response.data, isLoadingStates: false));

      // Select user's state if profile is loaded
      if (state.userProfile != null) {
        final userState = response.data.firstWhere(
          (s) => s.name == state.userProfile!.data.state,
          // orElse: () => null,
        );
        // ignore: unnecessary_null_comparison
        if (userState != null) add(SelectStateEvent(userState));
      }
    } catch (e) {
      emit(state.copyWith(isLoadingStates: false));
    }
  }

  Future<void> _onLoadCities(
    LoadCitiesEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    emit(state.copyWith(isLoadingCities: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final response = await apiClient.getDemographyList(loginuserId, brandId.toString(), 3, event.stateId);
      emit(state.copyWith(cities: response.data, isLoadingCities: false));

      // Select user's city if profile is loaded
      if (state.userProfile != null) {
        final userCity = response.data.firstWhere(
          (c) => c.name == state.userProfile?.data.city,
          // orElse: () => null,
        );
        // ignore: unnecessary_null_comparison
        if (userCity != null) add(SelectCityEvent(userCity));
      }
    } catch (e) {
      emit(state.copyWith(isLoadingCities: false));
    }
  }

  void _onSelectCountry(
    SelectCountryEvent event,
    Emitter<UserProfileState> emit,
  ) {
    emit(state.copyWith(
      selectedCountry: event.country,
      selectedState: state.states.firstWhere(
        (country) => country.name == '',
        orElse: () => DemographyItem(name: '', requestType: 0, forwardingId: 0),
      ),
      selectedCity: state.cities.firstWhere(
        (country) => country.name == '',
        orElse: () => DemographyItem(name: '', requestType: 0, forwardingId: 0),
      ),
      states: [],
      cities: [],
      countryController: TextEditingController(text: event.country?.name ?? ""),
      stateController: TextEditingController(),
      cityController: TextEditingController(),
    ));
    // Load states for new country
    if (event.country?.forwardingId != null) {
      add(LoadStatesEvent(event.country!.forwardingId));
    }
  }

  void _onSelectState(
    SelectStateEvent event,
    Emitter<UserProfileState> emit,
  ) {
    emit(state.copyWith(
      selectedState: event.state,
      selectedCity: state.cities.firstWhere(
        (country) => country.name == '',
        orElse: () => DemographyItem(name: '', requestType: 0, forwardingId: 0),
      ),
      cities: [],
      stateController: TextEditingController(text: event.state?.name ?? ""),
      cityController: TextEditingController(),
    ));
    // Load cities for new state
    if (event.state?.forwardingId != null) {
      add(LoadCitiesEvent(event.state!.forwardingId));
    }
  }

  void _onSelectCity(
    SelectCityEvent event,
    Emitter<UserProfileState> emit,
  ) {
    emit(state.copyWith(
      selectedCity: event.city,
      cityController: TextEditingController(text: event.city?.name ?? ""),
    ));
  }

  _onDeleteHighLightStoryEvent(DeleteHighLightStoryEvent event, Emitter<UserProfileState> emit) async {
    emit(state.copyWith(isloding: true));
    try {
      final result = await apiClient.highlightsDelete(id: event.storyId);

      if (result.status == true) {
        NavigatorService.goBack();
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message!,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        NavigatorService.goBack();
        emit(state.copyWith(isloding: false));
      } else {
        emit(state.copyWith(isloding: false));
        toastification.show(
          type: ToastificationType.error,
          title: Text("Failed to delete highlight."),
        );
      }
    } catch (e) {
      emit(state.copyWith(isloding: false));
      progressNotifier.value = 0.0;
      Logger.lOG(e.toString());
    }
  }
}
