// ignore_for_file: deprecated_member_use, unused_field, prefer_final_fields, use_build_context_synchronously, unused_element, depend_on_referenced_packages

import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:detectable_text_field/detectable_text_field.dart';

// import 'package:ffmpeg_kit_flutter_min/ffmpeg_kit.dart';
// import 'package:ffmpeg_kit_flutter_min/return_code.dart';
import 'package:flowkar/core/utils/date_time_utils.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/upload_post/presentation/widget/industry_bottomsheet.dart';
// import 'package:flowkar/features/widgets/common/app_bottom_sheet.dart';
import 'package:flowkar/features/widgets/common/mention_list_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_popup/flutter_popup.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_asker/permission_asker.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:uuid/uuid.dart';
import 'package:video_player/video_player.dart';
import 'package:path/path.dart' as path;
import 'package:flowkar/features/upload_post/bloc/post_bloc.dart';
import 'package:easy_video_editor/easy_video_editor.dart';

class UploadPostScreen extends StatefulWidget {
  const UploadPostScreen({super.key});
  static Widget builder(BuildContext context) {
    return UploadPostScreen();
  }

  @override
  State<UploadPostScreen> createState() => _UploadPostScreenState();
}

class _UploadPostScreenState extends State<UploadPostScreen> {
  // final TextEditingController postContentController = TextEditingController();
  final TextEditingController postTitleController = TextEditingController();
  final TextEditingController scheduledTimeContentController = TextEditingController();
  DetectableTextEditingController postContentController = DetectableTextEditingController();

  final ImagePicker _picker = ImagePicker();
  List<File> _images = [];
  List<File> _postMedia = [];
  var isEnableAllSocialMedia = false;
  int _selectedImageIndex = 0;
  List<bool> _platformSwitches = socialPlatformsStatus.value.values.toList();
  bool _shareWith = socialPlatformsStatus.value.values.contains(true);
  final bool _isSwitchEnabled = true;
  DateTime? _selectedDateTime;
  VideoPlayerController? _videoPlayerController;
  bool _isPlaying = false;
  List<File> _videoThumbnail = [];
  // Teg User
  List<int?> taguserIdList = [];
  List<Map<String, String>> tagUserList = [];
  DetectableTextEditingController descriptionController = DetectableTextEditingController();
  final TextEditingController locationController = TextEditingController();
  final TextEditingController searchlocationController = TextEditingController();
  final TextEditingController searchIndustryController = TextEditingController();
  PageController controller = PageController();
  bool isPrivate = false;
  List<dynamic> args = [];
  bool isVideoplatform = false;
  bool isImageplatform = false;
  bool isbothplatform = false;
  bool isTextplatform = false;
  List<File> videoList = [];
  bool isProcessing = false;
  List<VideoItem> processedVideos = [];
  bool isVideoChunkEnabled = false; // Track switch state
  bool isVideoEligibleForChunk = false;
  bool isfirstTime = true;
  List<String?> hashtagsList = [];
  late String industryName = '';
  int? industryId;
  Position? position;
  bool isTextPost = false;
  bool _showDescriptionError = false;
  bool isSocailMediaConnected = false;

  bool isLoading = true;
  bool isGeneratedDuplicate = false;

  @override
  void initState() {
    super.initState();

    fetchAndSearchLocation(context);

    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   Future.delayed(Duration.zero, () async {
    //     final args =
    //         ModalRoute.of(context)?.settings.arguments as List<dynamic>?;
    //     if (args != null && args.isNotEmpty) {
    //       List<String> imagePaths = List<String>.from(args[0]);
    //       setState(() {
    //         _postMedia = imagePaths.map((path) => File(path)).toList();
    //         if (_postMedia.isNotEmpty && !_isImage(_postMedia.first)) {
    //           _initializeVideoPlayer(_postMedia.first);
    //         }
    //       });
    //     }
    //   });
    // }

    // );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(Duration.zero, () async {
        final args = ModalRoute.of(context)?.settings.arguments as List<dynamic>?;
        if (args != null && args.isNotEmpty) {
          List<String> mediaPaths = List<String>.from(args[0]);
          isTextPost = args[1];
          isGeneratedDuplicate = args.length > 2 ? args[2] == true : false;
          postContentController.text = args.length > 3 ? args[3] : '';
          if (args[3] != null && args[3].isNotEmpty) {
            extractHashtags(args[3]);
          }

          setState(() {
            _postMedia = mediaPaths.map((path) => File(path)).toList();

            bool hasImage = _postMedia.any((file) => _isImage(file));
            bool hasVideo = _postMedia.any((file) => !_isImage(file));

            // Manage platform states
            isImageplatform = hasImage && !hasVideo;
            isVideoplatform = hasVideo && !hasImage;
            isbothplatform = hasImage && hasVideo;
            isTextplatform = isTextPost;

            // Initialize video player if there is a video
            if (hasVideo) {
              _initializeVideoPlayer(_postMedia.firstWhere((file) => !_isImage(file)));
            }

            // Reset platform switches based on content type
            _platformSwitches = socialPlatformsStatus.value.entries.map((entry) {
              String platformName = entry.key;
              bool isConnected = entry.value;

              if (!isConnected) return false;

              bool isSupported = false;
              if (isTextplatform) {
                isSupported = allowsText(platformName);
              } else if (isVideoplatform) {
                isSupported = allowsOnlyVideo(platformName) || allowsBoth(platformName);
              } else if (isImageplatform) {
                isSupported = allowsOnlyImage(platformName) || allowsBoth(platformName);
              } else if (isbothplatform) {
                isSupported = allowsBoth(platformName);
              }

              return isSupported;
            }).toList();

            _shareWith = _platformSwitches.contains(true);

            Logger.lOG(isImageplatform);
            Logger.lOG(isVideoplatform);
            Logger.lOG(isbothplatform);
            Logger.lOG(isTextplatform);
          });
        }
      });
    });
    // if (!isTextPost) requestStoragePermission();

    // _initializeVideoPlayer()
  }

  // Future<void> processVideo(File video) async {
  //   showLoadingOverlay(context);

  //   setState(() {
  //     isProcessing = true;
  //     videoList.clear();
  //   });

  //   try {
  //     Logger.lOG('Starting video processing: ${video.path}');

  //     // Check if source video exists and is readable
  //     if (!await video.exists()) {
  //       throw Exception('Source video file does not exist: ${video.path}');
  //     }

  //     // Initialize video controller to get duration
  //     final controller = VideoPlayerController.file(video);
  //     await controller.initialize().catchError((error) {
  //       Logger.lOG('Failed to initialize VideoPlayerController: $error');
  //       throw Exception('Could not initialize video: $error');
  //     });

  //     final int durationInSeconds = controller.value.duration.inSeconds;
  //     Logger.lOG('Video duration: $durationInSeconds seconds');
  //     await controller.dispose();

  //     final int numberOfChunks = (durationInSeconds / 90).ceil();
  //     Logger.lOG('Will create $numberOfChunks chunks (90 seconds each)');

  //     final videoItem = VideoItem(path: video.path, name: path.basename(video.path), totalChunks: numberOfChunks);

  //     // Create directory for chunks
  //     final Directory appDir = await getApplicationDocumentsDirectory();
  //     final String videoName = path.basenameWithoutExtension(video.path);
  //     final Directory chunkDir = Directory('${appDir.path}/chunks/$videoName');

  //     try {
  //       if (!await chunkDir.exists()) {
  //         await chunkDir.create(recursive: true);
  //         Logger.lOG('Created chunks directory: ${chunkDir.path}');
  //       } else {
  //         Logger.lOG('Using existing directory: ${chunkDir.path}');
  //       }
  //     } catch (e) {
  //       Logger.lOG('Failed to create directory: $e');
  //       throw Exception('Failed to create directory for chunks: $e');
  //     }

  //     // Process each chunk
  //     for (int i = 0; i < numberOfChunks; i++) {
  //       final int startTime = i * 90;
  //       final int endTime = ((startTime + 90) > durationInSeconds) ? durationInSeconds : (startTime + 90);
  //       final String outputPath = '${chunkDir.path}/chunk_${Uuid().v4()}.mp4';

  //       Logger.lOG('Processing chunk $i: $startTime to $endTime seconds');
  //       Logger.lOG('Output path: $outputPath');

  //       try {
  //         final videoEditor = VideoEditorBuilder(videoPath: video.path);

  //         // Add progress tracking
  //         int lastProgress = 0;
  //         await videoEditor.trim(startTimeMs: startTime * 1000, endTimeMs: endTime * 1000).export(
  //             outputPath: outputPath,
  //             onProgress: (progress) {
  //               // Log progress at 20% intervals to avoid excessive logging
  //               int currentProgress = (progress * 100).toInt();
  //               if (currentProgress % 20 == 0 && currentProgress != lastProgress) {
  //                 Logger.lOG('Chunk $i export progress: $currentProgress%');
  //                 lastProgress = currentProgress;
  //               }
  //             });

  //         // Verify the exported file
  //         final File outputFile = File(outputPath);
  //         if (await outputFile.exists()) {
  //           final int fileSize = await outputFile.length();

  //           if (fileSize > 1024) {
  //             // Ensure file is larger than 1KB to be valid
  //             Logger.lOG('✅ Chunk $i successfully written: $outputPath (${fileSize ~/ 1024} KB)');
  //             videoList.add(outputFile);
  //             videoItem.chunkPaths.add(outputPath);
  //           } else {
  //             Logger.lOG('⚠️ Chunk $i created but file is too small: $fileSize bytes');
  //             throw Exception('Created file is too small, likely corrupted');
  //           }
  //         } else {
  //           Logger.lOG('❌ Chunk $i failed — file does not exist at $outputPath');
  //           throw Exception('Output file was not created');
  //         }
  //       } catch (e, stackTrace) {
  //         Logger.lOG('❌ Failed to create chunk $i. Error: $e');
  //         Logger.lOG('Stack trace: $stackTrace');

  //         // Continue with the next chunk instead of stopping the entire process
  //         continue;
  //       }

  //       // Add a small delay between chunks to prevent resource contention
  //       await Future.delayed(Duration(milliseconds: 500));
  //     }

  //     setState(() {
  //       processedVideos.add(videoItem);
  //       videoList = processedVideos.expand((videoItem) => videoItem.chunkPaths.map((path) => File(path))).toList();

  //       Logger.lOG(
  //           '🎬 Video "${videoItem.name}" processed into ${videoItem.chunkPaths.length}/$numberOfChunks chunks.');
  //     });
  //   } catch (e, stackTrace) {
  //     Logger.lOG('❗ Error processing video: $e');
  //     Logger.lOG('Stack trace: $stackTrace');

  //     // Show error to user
  //     toastification.show(
  //       type: ToastificationType.error,
  //       showProgressBar: false,
  //       description: Text(
  //         "Video chunking failed: ${e.toString().substring(0, min(e.toString().length, 100))}",
  //         style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 12.0.sp),
  //       ),
  //       autoCloseDuration: const Duration(seconds: 4),
  //     );
  //   } finally {
  //     setState(() => isProcessing = false);
  //     Logger.lOG("Processing finished!");
  //     Navigator.of(context, rootNavigator: true).pop(); // Hide loader
  //   }
  // }
  Future<void> processVideo(File video) async {
    showLoadingOverlay(context);

    // Start performance monitoring
    PerformanceMonitor.startTimer('video_processing');

    setState(() {
      isProcessing = true;
      videoList.clear();
    });

    try {
      Logger.lOG('Starting video processing: ${video.path}');

      // Check if source video exists and is readable
      if (!await video.exists()) {
        throw Exception('Source video file does not exist: ${video.path}');
      }

      // Initialize video controller to get duration
      final controller = VideoPlayerController.file(video);
      await controller.initialize().catchError((error) {
        Logger.lOG('Failed to initialize VideoPlayerController: $error');
        throw Exception('Could not initialize video: $error');
      });

      final int durationInSeconds = controller.value.duration.inSeconds;
      Logger.lOG('Video duration: $durationInSeconds seconds');
      await controller.dispose();

      final int numberOfChunks = (durationInSeconds / 90).ceil();
      Logger.lOG('Will create $numberOfChunks chunks (90 seconds each)');

      final videoItem = VideoItem(path: video.path, name: path.basename(video.path), totalChunks: numberOfChunks);

      // Create directory for chunks
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String videoName = path.basenameWithoutExtension(video.path);
      final Directory chunkDir = Directory('${appDir.path}/chunks/$videoName');

      try {
        if (!await chunkDir.exists()) {
          await chunkDir.create(recursive: true);
          Logger.lOG('Created chunks directory: ${chunkDir.path}');
        } else {
          Logger.lOG('Using existing directory: ${chunkDir.path}');
        }
      } catch (e) {
        Logger.lOG('Failed to create directory: $e');
        throw Exception('Failed to create directory for chunks: $e');
      }

      // Process chunks in optimized batches for better performance
      final int maxConcurrentChunks = Platform.isIOS ? 1 : 2; // iOS is more memory constrained
      Logger.lOG('Processing with max $maxConcurrentChunks concurrent chunks');

      for (int i = 0; i < numberOfChunks; i += maxConcurrentChunks) {
        final int endIndex = (i + maxConcurrentChunks > numberOfChunks) ? numberOfChunks : i + maxConcurrentChunks;

        // Process chunks in batches
        final List<Future<void>> batchFutures = [];
        for (int j = i; j < endIndex; j++) {
          batchFutures.add(_processChunk(video, j, durationInSeconds, chunkDir, videoItem));
        }

        // Wait for current batch to complete before starting next batch
        await Future.wait(batchFutures);

        // Update UI with progress
        setState(() {
          // Force UI update to show progress
        });

        // Minimal delay between batches to prevent resource exhaustion
        if (endIndex < numberOfChunks) {
          await Future.delayed(Duration(milliseconds: 100));
        }
      }

      // End performance monitoring
      PerformanceMonitor.endTimer('video_processing');

      setState(() {
        processedVideos.add(videoItem);
        videoList = processedVideos.expand((videoItem) => videoItem.chunkPaths.map((path) => File(path))).toList();

        Logger.lOG(
            '🎬 Video "${videoItem.name}" processed into ${videoItem.chunkPaths.length}/$numberOfChunks chunks.');
      });
    } catch (e, stackTrace) {
      Logger.lOG('❗ Error processing video: $e');
      Logger.lOG('Stack trace: $stackTrace');

      // End performance monitoring on error
      PerformanceMonitor.endTimer('video_processing');

      // Show error to user
      toastification.show(
        type: ToastificationType.error,
        showProgressBar: false,
        description: Text(
          "Video chunking failed: ${e.toString().substring(0, min(e.toString().length, 100))}",
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 12.0.sp),
        ),
        autoCloseDuration: const Duration(seconds: 4),
      );
    } finally {
      setState(() => isProcessing = false);
      Logger.lOG("Processing finished!");
      Navigator.of(context, rootNavigator: true).pop(); // Hide loader
    }
  }

  Future<void> _processChunk(
      File video, int chunkIndex, int durationInSeconds, Directory chunkDir, VideoItem videoItem) async {
    final int startTime = chunkIndex * 90;
    final int endTime = ((startTime + 90) > durationInSeconds) ? durationInSeconds : (startTime + 90);
    final String outputPath = '${chunkDir.path}/chunk_${Uuid().v4()}.mp4';

    Logger.lOG('Processing chunk $chunkIndex: $startTime to $endTime seconds');

    // Start timing individual chunk processing
    PerformanceMonitor.startTimer('chunk_$chunkIndex');

    try {
      final videoEditor = VideoEditorBuilder(videoPath: video.path);

      // Optimized export for faster processing with minimal progress tracking
      await videoEditor.trim(startTimeMs: startTime * 1000, endTimeMs: endTime * 1000).export(
          outputPath: outputPath,
          onProgress: (progress) {
            // Only log completion to reduce overhead
            if (progress == 1.0) {
              Logger.lOG('Chunk $chunkIndex export completed');
            }
          });

      // Verify the exported file efficiently
      final File outputFile = File(outputPath);
      if (await outputFile.exists()) {
        final int fileSize = await outputFile.length();

        if (fileSize > 1024) {
          // Ensure file is larger than 1KB to be valid
          Logger.lOG('✅ Chunk $chunkIndex: ${fileSize ~/ 1024} KB');
          videoList.add(outputFile);
          videoItem.chunkPaths.add(outputPath);

          // End timing for successful chunk
          PerformanceMonitor.endTimer('chunk_$chunkIndex');
        } else {
          Logger.lOG('⚠️ Chunk $chunkIndex too small: $fileSize bytes');
          PerformanceMonitor.endTimer('chunk_$chunkIndex');
          throw Exception('Created file is too small, likely corrupted');
        }
      } else {
        Logger.lOG('❌ Chunk $chunkIndex file not created');
        PerformanceMonitor.endTimer('chunk_$chunkIndex');
        throw Exception('Output file was not created');
      }
    } catch (e) {
      Logger.lOG('❌ Chunk $chunkIndex failed: $e');
      PerformanceMonitor.endTimer('chunk_$chunkIndex');
    }
  }

  void _initializeVideoPlayer(File videoFile) {
    _videoPlayerController?.dispose();
    _videoPlayerController = VideoPlayerController.file(videoFile)
      ..initialize().then((_) {
        setState(() {
          final int durationInSeconds = _videoPlayerController!.value.duration.inSeconds;

          if (isfirstTime) {
            if (durationInSeconds > 0 && durationInSeconds <= 90) {
              setState(() {
                isVideoEligibleForChunk = false;
                isfirstTime = false;
                Logger.lOG('-*******-- $isVideoEligibleForChunk');
              });
              Logger.lOG('Video is less than 90 seconds. Showing ListTile.');
            } else {
              setState(() {
                isVideoEligibleForChunk = true;
                isfirstTime = false;
                Logger.lOG('----- $isVideoEligibleForChunk');
              });
              Logger.lOG('Video is more than 90 seconds. Hiding ListTile.');
            }
          }
        });
      });
  }

  Future<void> fetchAndSearchLocation(BuildContext context) async {
    try {
      Position position = await _getCurrentLocation();

      // Fire the event with the obtained location
      context.read<PostBloc>().add(
            SearchLocationEvent(
              searchtext: "",
              lat: position.latitude.toString(),
              long: position.longitude.toString(),
            ),
          );
    } catch (e) {
      debugPrint("Location error: $e");
      // Optionally handle error (e.g., show snackbar or dialog)
    }
  }

  Future<Position> _getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      bool enableService = await showDialog(
        context: context,
        builder: (context) => CustomAlertDialog(
          title: 'Location Service Disabled',
          subtitle: 'Please enable location services. So, we can show your current location.',
          confirmButtonText: "Enable",
          onCancelButtonPressed: () => Navigator.pop(context, false),
          onConfirmButtonPressed: () => Navigator.pop(context, true),
        ),
      );

      if (enableService == true) {
        serviceEnabled = await Geolocator.openLocationSettings();
        if (!serviceEnabled) {
          throw Exception('Location services still disabled after user request');
        }
      } else {
        throw Exception('Location services required');
      }
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        bool shouldRequestAgain = await showDialog(
          context: context,
          builder: (context) => CustomAlertDialog(
            title: 'Location Permission Required',
            subtitle: 'This feature requires location permission to work properly.',
            confirmButtonText: "Request Again",
            onCancelButtonPressed: () => Navigator.pop(context, false),
            onConfirmButtonPressed: () => Navigator.pop(context, true),
          ),
        );

        if (shouldRequestAgain == true) {
          permission = await Geolocator.requestPermission();
        }

        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }
    }

    if (permission == LocationPermission.deniedForever) {
      bool openSettings = await showDialog(
        context: context,
        builder: (context) => CustomAlertDialog(
          title: 'Location Permission Permanently Denied',
          subtitle: 'Please enable location permissions in app settings.',
          confirmButtonText: "Open Settings",
          onCancelButtonPressed: () => Navigator.pop(context, false),
          onConfirmButtonPressed: () => Navigator.pop(context, true),
        ),
      );

      if (openSettings == true) {
        await Geolocator.openAppSettings();
        permission = await Geolocator.checkPermission();
        if (permission != LocationPermission.whileInUse && permission != LocationPermission.always) {
          throw Exception('Location permissions not granted after settings');
        }
      } else {
        throw Exception('Location permissions are permanently denied');
      }
    }

    return await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
  }

  @override
  void dispose() {
    _videoPlayerController?.dispose();
    super.dispose();
  }

  Future<void> requestStoragePermission() async {
    final status = await Permission.storage.request();
    final camera = await Permission.camera.request();
    final microphone = await Permission.microphone.request();
    final manageExternalStorage = await Permission.manageExternalStorage.request();
    if (status.isGranted && camera.isGranted && microphone.isGranted && manageExternalStorage.isGranted) {
      Logger.lOG('Storage permission granted');
    } else {
      Logger.lOG('Storage permission denied');
    }
  }

  Future<void> _uploadPost({isposts}) async {
    if (isTextPost) {
      if (postContentController.text.trim().isEmpty) {
        setState(() {
          _showDescriptionError = true;
        });
        return;
      }
    }

    String updatedText = postContentController.text.toString().trim();
    updatedText.trim();

    bool containsVideo = _postMedia.any((file) => isVideo(file.path));
    Logger.lOG("_postMedia length $_postMedia");

    if (containsVideo) {
      File? thumbnail = await getVideoThumbnail(_postMedia.first.path);

      if (thumbnail != null) {
        setState(() {
          _videoThumbnail = [thumbnail];
        });
        Logger.lOG('Video thumbnail generated: $thumbnail');
      }

      Logger.lOG('There is at least one video in the list');
    } else {
      Logger.lOG('No videos found');
    }

    for (var tagUser in tagUserList) {
      if (tagUser['user_name'] != null) {
        var userId = tagUser['user_id'];
        Logger.lOG(userId);
        if (userId != null && !taguserIdList.contains(int.parse(userId))) {
          Logger.lOG("Adding User ID: $userId for ${tagUser['user_name']}");
          taguserIdList.add(int.parse(userId));
          Logger.lOG("Updated taguserIdList: $taguserIdList");
        }
      }
    }
    Logger.lOG("x ------ [4] ${_platformSwitches[10]}");
    Logger.lOG("isInstagram: _platformSwitches[2] ${_platformSwitches[2]}");
    Logger.lOG("isThread: _platformSwitches[3],${_platformSwitches[3]}");
    Logger.lOG("industry: industryId,$industryId");
    Logger.lOG("locationController.text.trim(),${locationController.text.trim()}");
    Logger.lOG("taggedin: taguserIdList.whereType<int>().toList(), ,${taguserIdList.whereType<int>().toList()}");
    Logger.lOG("hashtags: hashtagsList.whereType<String>().toList(),${hashtagsList.whereType<String>().toList()}");

    context.read<PostBloc>().add(
          UploadPostAPIEvent(
            context: context,
            isTextPost: isTextPost == true ? true : false,
            images: isTextPost ? [] : (isVideoChunkEnabled == false ? _postMedia : videoList),
            isVideo: isTextPost ? false : !_isImage(_postMedia[_selectedImageIndex]),
            description: postContentController.text.trim(),
            title: postTitleController.text.trim(),
            location: locationController.text.trim(),
            isVimeo: isTextPost ? false : (isVideoplatform ? _platformSwitches[0] : false),
            isFacebook: _platformSwitches[1],
            isInstagram: _platformSwitches[2],
            isThread: _platformSwitches[3],
            isLinkdedin: _platformSwitches[4],
            isPintrest: isTextPost ? false : (isImageplatform ? _platformSwitches[5] : false),
            isTumblr: _platformSwitches[6],
            isReddit: _platformSwitches[7],
            isYoutube: isTextPost ? false : (isVideoplatform ? _platformSwitches[8] : false),
            isTikTok: isTextPost ? false : (isVideoplatform ? _platformSwitches[9] : false),
            x: _platformSwitches[10],
            mastadon: _platformSwitches[12],
            videoThumbnail: isTextPost ? [] : (_isImage(_postMedia[_selectedImageIndex]) ? [] : _videoThumbnail),
            scheduledat: _selectedDateTime == null ? "" : _selectedDateTime.toString(),
            isprivate: isPrivate,
            taggedin: taguserIdList.whereType<int>().toList(),
            hashtags: hashtagsList.whereType<String>().toList(),
            industry: industryId ?? 0,
            isPost: isposts,
          ),
        );
    // context.read<PostBloc>().add(
    //       UploadPostAPIEvent(
    //           context: context,
    //           isTextPost: true,
    //           images: isVideoChunkEnabled == false ? _postMedia : videoList,
    //           isVideo: _isImage(_postMedia[_selectedImageIndex]) ? false : true,
    //           description: postContentController.text.trim(),
    //           title: postTitleController.text.trim(),
    //           location: locationController.text.trim(),
    //           isVimeo: isVideoplatform ? _platformSwitches[0] : false,
    //           isFacebook: _platformSwitches[1],
    //           isInstagram: _platformSwitches[2],
    //           isThread: _platformSwitches[3],
    //           isLinkdedin: _platformSwitches[4],
    //           isPintrest: isImageplatform ? _platformSwitches[5] : false,
    //           isTumblr: _platformSwitches[6],
    //           isReddit: _platformSwitches[7],
    //           isYoutube: isVideoplatform ? _platformSwitches[8] : false,
    //           isTikTok: isVideoplatform ? _platformSwitches[9] : false,
    //           videoThumbnail: _isImage(_postMedia[_selectedImageIndex]) ? [] : _videoThumbnail,
    //           scheduledat: _selectedDateTime == null ? "" : _selectedDateTime.toString(),
    //           isprivate: isPrivate,
    //           taggedin: taguserIdList.whereType<int>().toList(),
    //           hashtags: hashtagsList.whereType<String>().toList(),
    //           industry: industryId ?? 0,
    //           isPost: isposts),
    //     );
    if (isposts) {
      NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar);
    }

    postContentController.clear();
    postTitleController.clear();
  }

  void _updatePlatformSwitches(bool value) {
    setState(() {
      _platformSwitches = socialPlatformsStatus.value.entries.map((entry) {
        String platformName = entry.key;
        bool isConnected = entry.value;

        if (!isConnected) return false;

        bool isSupported = false;
        if (isTextplatform) {
          isSupported = allowsText(platformName);
        } else if (isVideoplatform) {
          isSupported = allowsOnlyVideo(platformName) || allowsBoth(platformName);
        } else if (isImageplatform) {
          isSupported = allowsOnlyImage(platformName) || allowsBoth(platformName);
        } else if (isbothplatform) {
          isSupported = allowsBoth(platformName);
        }

        return isSupported ? value : false;
      }).toList();

      _shareWith = _platformSwitches.contains(true);
    });
  }

  // Remove a user from the tag list
  void removeTag(String userId, String userName) {
    setState(() {
      tagUserList.removeWhere((user) => user['user_id'] == userId);
      taguserIdList.removeWhere((id) => id.toString() == userId);
      // Remove the username from description text
      String newText = descriptionController.text.replaceAll(userName, "").trim();
      descriptionController.text = newText;
    });
  }

  @override
  Widget build(BuildContext context) {
    List<SocialPlatform> socialPlatforms = [
      SocialPlatform(
        platformName: "Vimeo",
        icon: Assets.images.pngs.socialConnect.pngVimeo.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['VIMEO'] ?? false,
      ),
      SocialPlatform(
        platformName: "Facebook",
        icon: Assets.images.icons.social.facebook.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['FACEBOOK'] ?? false,
      ),
      SocialPlatform(
        platformName: "Instagram",
        icon: Assets.images.icons.social.insta.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['INSTAGRAM'] ?? false,
      ),
      SocialPlatform(
        platformName: "Thread",
        icon: Assets.images.icons.social.icThread.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['THREAD'] ?? false,
      ),
      SocialPlatform(
        platformName: "Linkedin",
        icon: Assets.images.icons.social.linkedin.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['LINKEDIN'] ?? false,
      ),
      SocialPlatform(
          platformName: "Pinterest",
          icon: Assets.images.icons.social.pintrest.path,
          onTap: () {},
          isEnabled: socialPlatformsStatus.value['PINTEREST'] ?? false),
      SocialPlatform(
        platformName: "Tumblr",
        icon: Assets.images.pngs.socialConnect.icTumblr.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['TUMBLR'] ?? false,
      ),
      SocialPlatform(
        platformName: "Reddit",
        icon: Assets.images.pngs.socialConnect.pngReddit.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['REDDIT'] ?? false,
      ),
      SocialPlatform(
        platformName: "Youtube",
        icon: Assets.images.icons.social.icYoutube.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['YOUTUBE'] ?? false,
      ),
      SocialPlatform(
        platformName: "Tiktok",
        icon: Assets.images.icons.social.icTictok.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['TIKTOK'] ?? false,
      ),
      SocialPlatform(
        platformName: "X",
        icon: Assets.images.pngs.socialConnect.pngTwitter.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['X'] ?? false,
      ),
      SocialPlatform(
        platformName: "Telegram",
        icon: Assets.images.icons.social.svgTelegram.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['TELEGRAM'] ?? false,
      ),
      SocialPlatform(
        platformName: "Mastodon",
        icon: Assets.images.icons.social.svgMastodon.path,
        onTap: () {},
        isEnabled: socialPlatformsStatus.value['MASTODON'] ?? false,
      ),
    ];
    return Scaffold(
      appBar: _buildUploadPostAppBar(),
      body: BlocBuilder<PostBloc, PostState>(
        builder: (context, state) {
          return IgnorePointer(
            ignoring: state.isUploading,
            child: SingleChildScrollView(
              child: InkWell(
                focusColor: Colors.transparent,
                onTap: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                },
                child: AbsorbPointer(
                  absorbing: state.isUploading,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 18.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        buildSizedBoxH(8.0),
                        isTextPost
                            ? SizedBox.shrink()
                            : _buildImagePickerWidget(
                                images: videoList.isEmpty ? _postMedia : videoList,
                                // onTap: _showMediaPickerDialog,
                                onRemove: (index) {
                                  setState(() {
                                    videoList.isEmpty ? _postMedia.removeAt(index) : videoList.removeAt(index);
                                    Logger.lOG("_postMedia length $_postMedia");
                                  });
                                },
                              ),
                        buildSizedBoxH(10.0),
                        if ((videoList.isEmpty ? _postMedia : videoList).length > 1)
                          Center(
                            child: SmoothPageIndicator(
                              controller: controller,
                              count: videoList.isEmpty ? _postMedia.length : videoList.length,
                              effect: WormEffect(
                                  dotWidth: 8.w, dotHeight: 8.h, activeDotColor: Theme.of(context).primaryColor),
                            ),
                          ),
                        buildSizedBoxH(20.0),
                        _buildPostContentField(),
                        buildSizedBoxH(20.0),
                        locationController.text.isEmpty
                            ? SizedBox.shrink()
                            : FlowkarTextFormField(
                                onTap: () async {
                                  isLoading = true;
                                  fetchAndSearchLocation(context);
                                  searchlocationController.text = locationController.text;
                                  showModalBottomSheet(
                                    context: context,
                                    isScrollControlled: true,
                                    useSafeArea: true,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(40.0.r),
                                      ),
                                    ),
                                    builder: (ctx) {
                                      return _locationbottomsheet();
                                    },
                                  );
                                },
                                suffixIcon: searchlocationController.text.isNotEmpty
                                    ? CustomImageView(
                                        imagePath: AssetConstants.icClose,
                                        height: 10.0.h,
                                        width: 10.0.w,
                                        margin: EdgeInsets.all(16.5),
                                        onTap: () {
                                          searchlocationController.clear();
                                          state.searchLocation.clear();
                                          locationController.clear();
                                          setState(() {});
                                        },
                                      )
                                    : null,
                                isCapitalized: true,
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z\s]')),
                                ],
                                textInputAction: TextInputAction.done,
                                borderDecoration: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.grey.shade300),
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                readOnly: true,
                                controller: locationController,
                                context: context,
                                labelText: "Location",
                                hintText: "Search Location...",
                              ),
                        buildSizedBoxH(20.0),
                        _buildTaggedInLocationPostButton(state),
                        isVideoplatform && isVideoEligibleForChunk
                            ? ListTile(
                                contentPadding: EdgeInsets.only(left: 16.0.w, right: 8.0.w),
                                title: Row(
                                  children: [
                                    Text(
                                      "Video Chunk",
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(color: Theme.of(context).primaryColor),
                                    ),
                                    buildSizedBoxW(8.0),
                                    CustomPopup(
                                      content: Text(
                                        "This feature allows your long videos to be posted on short supported platforms in to individual parts like Instagram,Shorts etc.",
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium
                                            ?.copyWith(color: const Color(0xFF637381)),
                                      ),
                                      child: Icon(
                                        Icons.info_outline_rounded,
                                        color: Theme.of(context).primaryColor,
                                        size: 16.sp,
                                      ),
                                    ),
                                  ],
                                ),
                                trailing: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    CupertinoSwitch(
                                      activeColor: Theme.of(context).primaryColor,
                                      value: isVideoChunkEnabled,
                                      onChanged: (_postMedia.isNotEmpty && !isImageplatform)
                                          ? (value) async {
                                              if (_postMedia.length > 1) {
                                                toastification.show(
                                                  type: ToastificationType.info,
                                                  showProgressBar: false,
                                                  description: Text(
                                                    "Please select only one video to enable this feature. Multiple videos are not supported for chunk processing.",
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium
                                                        ?.copyWith(fontSize: 12.0.sp),
                                                  ),
                                                  autoCloseDuration: const Duration(seconds: 4),
                                                );
                                                return;
                                              }

                                              if (!isVideoChunkEnabled && value) {
                                                // Only when switching from OFF to ON
                                                setState(() {
                                                  isProcessing = true; // Show progress
                                                });

                                                await Future.delayed(Duration(seconds: 2)); // Simulate loading

                                                await processVideo(_postMedia.first); // Call function

                                                setState(() {
                                                  isProcessing = false; // Hide progress
                                                  isVideoChunkEnabled = value; // Update switch state
                                                });
                                              } else {
                                                setState(() {
                                                  isVideoChunkEnabled = value;
                                                  videoList.clear();
                                                  // Directly update switch state if turning OFF
                                                  Logger.lOG(videoList);
                                                });
                                              }
                                            }
                                          : null,
                                    ),
                                    // if (isProcessing)
                                    //   Positioned(
                                    //     left: 8.w,
                                    //     child: CupertinoActivityIndicator(radius: 8), // Progress indicator on switch thumb
                                    //   ),
                                  ],
                                ),
                              )
                            : SizedBox.shrink(),
                        buildSizedBoxH(isVideoplatform ? 0 : 30.0),
                        isTextPost
                            ? SizedBox.shrink()
                            : (isEnableAllSocialMedia)
                                ? SizedBox.shrink()
                                : _buildshareWith(),
                        if (!isTextPost)
                          Divider(
                            color: Theme.of(context).primaryColor.withOpacity(0.2),
                          ),
                        buildSizedBoxH(5.0),
                        isTextPost ? SizedBox.shrink() : _buildSocialConnectList(socialPlatforms),
                        buildSizedBoxH(30.0),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void showLoadingOverlay(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(
          child: LoadingAnimationWidget(),
        );
      },
    );
  }

  PreferredSizeWidget _buildUploadPostAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          isTextPost ? "Upload Text Post" : Lang.current.lbl_upload_post,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
        Spacer(),
        BlocBuilder<PostBloc, PostState>(
          builder: (context, state) {
            return AbsorbPointer(absorbing: state.isUploading, child: _buildPostButton(state));
          },
        ),
      ],
    );
  }

  Widget _buildImagePickerWidget({
    required List<File> images,
    required Function(int) onRemove,
  }) {
    return GestureDetector(
      // onTap: _showMediaPickerDialog,
      child: AspectRatio(
        aspectRatio: 1.4,
        child: Container(
          height: 200.h,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: images.isEmpty
              ? Center(
                  child: Icon(
                    Icons.add_photo_alternate_outlined,
                    size: 50.sp,
                    color: Colors.grey[700],
                  ),
                )
              : PageView.builder(
                  controller: controller,
                  itemCount: images.length,
                  onPageChanged: (value) {
                    _videoPlayerController?.pause();
                    _isPlaying = false;
                    _selectedImageIndex = value;
                    if (images.isNotEmpty && !_isImage(images[value])) {
                      _initializeVideoPlayer(images[value]);
                    }
                    _initializeVideoPlayer(images[value]);
                  },
                  itemBuilder: (context, index) {
                    final file = images[index];
                    final isImage = _isImage(file);

                    return Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            image: isImage
                                ? DecorationImage(
                                    image: FileImage(images[index]),
                                    fit: BoxFit.contain,
                                  )
                                : null,
                          ),
                          child: !isImage
                              ? Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    if (_videoPlayerController?.value.isInitialized ?? false)
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(10.r),
                                        child: Center(
                                            child: AspectRatio(
                                                aspectRatio: _videoPlayerController!.value.aspectRatio,
                                                child: Center(child: VideoPlayer(_videoPlayerController!)))),
                                      ),
                                    GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          if (_videoPlayerController?.value.isPlaying ?? false) {
                                            _videoPlayerController?.pause();
                                            _isPlaying = false;
                                          } else {
                                            _videoPlayerController?.play();
                                            _isPlaying = true;
                                          }
                                        });
                                      },
                                      child: Container(
                                        decoration: BoxDecoration(color: Colors.white, shape: BoxShape.circle),
                                        child: Icon(
                                          _isPlaying ? Icons.pause_circle_filled : Icons.play_circle_fill_rounded,
                                          size: 45.sp,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : null,
                        ),
                        Positioned(
                          top: 8.h,
                          right: 8.w,
                          child: GestureDetector(
                            onTap: () {
                              // Directly remove the file and update state
                              setState(() {
                                setState(() {});
                                images.removeAt(index); // Directly modify the list
                                Logger.lOG("_postMedia_postMedia ${images.length}");
                                if (images.isEmpty) {
                                  NavigatorService.goBack();
                                }
                              });
                            },
                            // onTap: () => onRemove(index),
                            child: CircleAvatar(
                              radius: 12.r,
                              backgroundColor: Theme.of(context).primaryColor,
                              child: Icon(
                                Icons.close,
                                size: 15.sp,
                                color: Theme.of(context).customColors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
        ),
      ),
    );
  }

  bool _isImage(File file) {
    final extension = file.path.split('.').last.toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif'].contains(extension);
  }

  void extractHashtags(String text) {
    Set<String> uniqueHashtags = {};
    List<String> words = text.split(' ');

    for (var word in words) {
      if (word.startsWith('#') && word.length > 1) {
        String tag = word.substring(1); // # hatai didhelu
        if (!tag.contains(' ')) {
          uniqueHashtags.add('#$tag'); // Original hashtag add karyu
        }
      }
    }

    setState(() {
      hashtagsList = uniqueHashtags.toList();
    });
  }

  String? getPrompt(TextEditingController postTitleController, TextEditingController postContentController) {
    final title = postTitleController.text.trim();
    final content = postContentController.text.trim();

    if (title.isEmpty && content.isEmpty) {
      return null; // or '' if your backend expects an empty string
    } else if (title.isNotEmpty && content.isNotEmpty) {
      return '$title $content';
    } else if (title.isNotEmpty) {
      return title;
    } else {
      return content;
    }
  }

  Widget _buildLoadingPopup(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            LoadingAnimationWidget(),
            const SizedBox(height: 20),
            const Text(
              'Please wait, generating content...',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostContentField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FlowkarTextFormField(
          labelText: 'Title',
          controller: postTitleController,
          maxLines: 2,
          maxLength: 200,
          counter: SizedBox.shrink(),
          textInputAction: TextInputAction.done,
          borderDecoration: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12.r),
          ),
          context: context,
        ),
        buildSizedBoxH(20),
        BlocConsumer<PostBloc, PostState>(
          listenWhen: (previous, current) =>
              previous.isAIGeneratingloading != current.isAIGeneratingloading ||
              previous.aIgenerateContentModel != current.aIgenerateContentModel,
          listener: (context, state) {
            if (state.isAIGeneratingloading) {
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (_) => _buildLoadingPopup(context),
              );
            } else {
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }

              final generatedText = state.aIgenerateContentModel?.aiResponse;
              if (generatedText != null && generatedText.isNotEmpty) {
                postContentController.text = generatedText;
                extractHashtags(generatedText);
              }
            }
          },
          builder: (context, state) {
            return Stack(
              children: [
                DetectableTextField(
                  controller: postContentController,
                  textInputAction: TextInputAction.newline,
                  maxLines: 6,
                  minLines: 2,
                  maxLength: 1000,
                  decoration: InputDecoration(
                    alignLabelWithHint: true,
                    labelText: 'Description',
                    hintText: "Enter your Description...",
                    floatingLabelBehavior: FloatingLabelBehavior.auto,
                    border: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    errorText: _showDescriptionError && isTextPost && postContentController.text.trim().isEmpty
                        ? 'Description is required for text posts'
                        : null,
                    suffixIcon: SizedBox(),
                  ),
                  onChanged: (value) {
                    extractHashtags(value);
                    if (_showDescriptionError) {
                      setState(() {
                        _showDescriptionError = postContentController.text.trim().isEmpty;
                      });
                    }
                  },
                ),
                if (!isTextPost)
                  if (!isGeneratedDuplicate)
                    Positioned(
                      bottom: 28,
                      right: 8,
                      child: InkWell(
                        onTap: () {
                          final prompt = getPrompt(postTitleController, postContentController);
                          final media = videoList.isEmpty ? _postMedia : videoList;
                          context
                              .read<PostBloc>()
                              .add(AIgenerateContentEvent(prompt: prompt ?? "", media: media.first));
                        },
                        child: CircleAvatar(
                          radius: 18.r,
                          backgroundColor: Theme.of(context).primaryColor,
                          child: state.isAIGeneratingloading
                              ? CupertinoActivityIndicator(
                                  color: Colors.white,
                                )
                              : Icon(Icons.auto_fix_high_rounded, color: Colors.white, size: 20),
                        ),
                      ),
                    ),
              ],
            );
          },
        ),

        // SizedBox(height: 10),
        buildSizedBoxH(_selectedDateTime != null ? 16.0 : 0),
        _selectedDateTime != null
            ? FlowkarTextFormField(
                labelText: 'Scheduled Time',
                readOnly: true,
                controller: scheduledTimeContentController,
                textInputAction: TextInputAction.done,
                borderDecoration: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                suffixIcon: _selectedDateTime != null
                    ? IconButton(
                        onPressed: () {
                          setState(() {
                            _selectedDateTime = null;
                            scheduledTimeContentController.clear();
                          });
                        },
                        icon: Icon(
                          Icons.close_rounded,
                          color: Theme.of(context).primaryColor,
                          size: 20.sp,
                        ))
                    : SizedBox.shrink(),
                context: context,
                onTap: () {
                  _openSchedulePostCalendar(context);
                },
              )
            : SizedBox.shrink(),
        buildSizedBoxH(industryName.isNotEmpty ? 16.0 : 0),
        industryName.isNotEmpty
            ? FlowkarTextFormField(
                labelText: 'Industry',
                controller: searchIndustryController,
                textInputAction: TextInputAction.done,
                borderDecoration: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                readOnly: true,
                suffixIcon: industryName.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          setState(() {
                            industryName = '';
                            industryId = null;
                            searchIndustryController.clear();
                          });
                        },
                        icon: Icon(
                          Icons.close_rounded,
                          color: Theme.of(context).primaryColor,
                          size: 20.sp,
                        ))
                    : SizedBox.shrink(),
                context: context,
                onTap: () async {
                  final result = await showModalBottomSheet<Map<String, dynamic>>(
                    context: context,
                    isScrollControlled: true,
                    useSafeArea: true,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(40.0.r),
                      ),
                    ),
                    builder: (ctx) {
                      return buildIndustryBottomSheet(searchIndustryController);
                    },
                  );

                  if (result != null) {
                    industryName = result['name'];
                    industryId = result['id'];
                    searchIndustryController.text = industryName;

                    Logger.lOG('Selected Industry: $industryName (ID: $industryId)');

                    setState(() {});
                  }
                },
              )
            : SizedBox.shrink(),
        buildSizedBoxH(tagUserList.isNotEmpty ? 16.0 : 0),
        tagUserList.isNotEmpty
            ? Text(
                "Tagged User",
                style: Theme.of(context).textTheme.bodyLarge,
              )
            : SizedBox.shrink(),
        buildSizedBoxH(tagUserList.isNotEmpty ? 10.0 : 0),
        tagUserList.isNotEmpty ? _buildtegUserPost() : SizedBox.shrink()
      ],
    );
  }

  Widget _buildTaggedInLocationPostButton(PostState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // GestureDetector(
        //   onTap: () {
        //     showAudienceSheet(context);
        //   },
        //   child: CustomImageView(
        //     margin: EdgeInsets.only(left: 8.w),
        //     height: 40.h,
        //     width: 40.w,
        //     imagePath: Assets.images.svg.uploadPost.svgAudience.path,
        //   ),
        // ),
        GestureDetector(
          onTap: () async {
            isLoading = true;
            fetchAndSearchLocation(context);
            searchlocationController.text = locationController.text;
            state.searchLocation.isNotEmpty ? state.searchLocation.clear() : null;

            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              useSafeArea: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(40.0.r),
                ),
              ),
              builder: (ctx) {
                return _locationbottomsheet();
              },
            );
          },
          child: CustomImageView(
            margin: EdgeInsets.only(left: 8.w),
            height: 40.h,
            width: 40.w,
            imagePath: locationController.text.isNotEmpty
                ? Assets.images.svg.uploadPost.svgLocationfill.path
                : Assets.images.svg.uploadPost.svgLocation.path,
          ),
        ),
        GestureDetector(
          onTap: () {
            FocusNode focusNode = FocusNode();
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              useSafeArea: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(40.0.r),
                ),
              ),
              builder: (ctx) {
                return Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(20.0.r),
                    ),
                  ),
                  padding: EdgeInsets.only(top: 16.h, left: 12.w, right: 12.w),
                  child: Column(
                    children: [
                      buildSizedBoxH(10.0),
                      Row(
                        children: [
                          buildSizedBoxW(2.0),
                          InkWell(
                            onTap: () {
                              state.searchuserList.clear();
                              FocusScope.of(context).unfocus();
                              NavigatorService.goBack();
                            },
                            child: CustomImageView(
                              margin: EdgeInsets.all(5),
                              imagePath: AssetConstants.pngBack,
                              height: 18.0.h,
                            ),
                          ),
                          buildSizedBoxW(18.0),
                          Center(
                            child: Text(
                              "People",
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(fontSize: 16.5.sp, fontWeight: FontWeight.w700),
                            ),
                          ),
                          // Spacer()
                        ],
                      ),
                      buildSizedBoxH(25.0),
                      ValueListenableBuilder<TextEditingValue>(
                        valueListenable: descriptionController,
                        builder: (context, value, child) {
                          return SizedBox(
                            child: FlowkarTextFormField(
                              context: context,
                              controller: descriptionController,
                              focusNode: focusNode,
                              hintText: Lang.of(context).lbl_tag_hint,
                              textInputAction: TextInputAction.done,
                              fillColor: ThemeData().customColors.fillcolor,
                              filled: true,
                              borderDecoration: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(10.r)),
                                borderSide: BorderSide.none,
                              ),
                              onChanged: (searchtext) {
                                // if (searchtext.contains('@')) {
                                //   String usernameSearch = searchtext.split('@').last;
                                context.read<PostBloc>().add(SearchmentionUserListEvent(searchtext: searchtext.trim()));
                                // }
                              },
                              prefixIcon: CustomImageView(
                                imagePath: AssetConstants.icSearch,
                                height: 10,
                                width: 10,
                                margin: EdgeInsets.all(15.0),
                              ),
                              suffixIcon: descriptionController.text.isNotEmpty
                                  ? CustomImageView(
                                      imagePath: AssetConstants.icClose,
                                      height: 10.0.h,
                                      width: 10.0.w,
                                      margin: EdgeInsets.all(16.5),
                                      onTap: () {
                                        descriptionController.clear();
                                        state.searchuserList.clear();
                                        context.read<PostBloc>().add(ClearSearchUserListEvent());
                                        setState(() {});
                                      },
                                    )
                                  : null,
                            ),
                          );
                        },
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              BlocBuilder<PostBloc, PostState>(
                                builder: (context, state) {
                                  return _buildUserList(state);
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
            // .whenComplete(() {
            //   // FocusScope.of(context).requestFocus(focusNode);
            // });
          },
          child: CustomImageView(
            margin: EdgeInsets.only(left: 8.w),
            height: 40.h,
            width: 40.w,
            imagePath: tagUserList.isNotEmpty
                ? Assets.images.svg.uploadPost.svgTaggedfill.path
                : Assets.images.svg.uploadPost.svgTagged.path,
          ),
        ),

        if (!isTextPost)
          GestureDetector(
            onTap: () {
              _openSchedulePostCalendar(context);
            },
            child: CustomImageView(
              margin: EdgeInsets.only(left: 8.w),
              height: 40.h,
              width: 40.w,
              imagePath: _selectedDateTime != null
                  ? Assets.images.svg.uploadPost.svgScheduledFill.path
                  : Assets.images.svg.uploadPost.svgScheduled.path,
            ),
          ),
        // In your Post Upload screen:
        GestureDetector(
          onTap: () async {
            context.read<PostBloc>().add(GetPostIndustryDetailsEvent());

            final result = await showModalBottomSheet<Map<String, dynamic>>(
              context: context,
              isScrollControlled: true,
              useSafeArea: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(40.0.r),
                ),
              ),
              builder: (ctx) {
                return buildIndustryBottomSheet(searchIndustryController);
              },
            );

            if (result != null) {
              industryName = result['name'];
              industryId = result['id'];
              searchIndustryController.text = industryName;

              Logger.lOG('Selected Industry: $industryName (ID: $industryId)');

              setState(() {});
            }
          },
          child: CustomImageView(
            margin: EdgeInsets.only(left: 8.w),
            height: 40.h,
            width: 40.w,
            imagePath: searchIndustryController.text.isNotEmpty // Or use a specific industry controller
                ? Assets.images.svg.uploadPost.svgIndustryFill.path
                : Assets.images.svg.uploadPost.svgIndustryUnfill.path,
          ),
        ),
        Spacer(),
        if (!isTextPost)
          BlocBuilder<PostBloc, PostState>(
            builder: (context, postState) {
              return CustomElevatedButton(
                width: 100.w,
                height: 40.h,
                onPressed: () {
                  showDialog(
                    barrierDismissible: postState.isUploading,
                    context: context,
                    builder: (context) {
                      return WillPopScope(
                        onWillPop: () async => false,
                        child: BlocBuilder<PostBloc, PostState>(
                          builder: (context, postState) {
                            return CustomAlertDialog(
                              title: "Save post as Draft",
                              onConfirmButtonPressed: () {
                                _uploadPost(isposts: false);
                              },
                              confirmButtonText: "Yes",
                              cancelButtonText: "No",
                              subtitle: 'Are you sure you want to save this post as Draft ?',
                              isLoading: postState.isUploading,
                            );
                          },
                        ),
                      );
                    },
                  );
                },
                isLoading: postState.isUploading,
                isDisabled: postState.isUploading,
                fontSize: 13.sp,
                brderRadius: 8.r,
                text: "Draft",
              );
            },
          )
      ],
    );
  }

  Widget _locationbottomsheet() {
    return StatefulBuilder(
      builder: (context, setState) {
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            setState(() {
              isLoading = false;
            });
          }
        });

        // Start a 2-second timer once the sheet opens

        return BlocBuilder<PostBloc, PostState>(
          builder: (context, state) {
            return Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20.0.r)),
              ),
              padding: EdgeInsets.only(top: 16.h, left: 12.w, right: 12.w),
              child: Column(
                children: [
                  buildSizedBoxH(16.0),
                  Row(
                    children: [
                      buildSizedBoxW(2.0),
                      InkWell(
                        onTap: () {
                          FocusScope.of(context).unfocus();
                          NavigatorService.goBack();
                        },
                        child: CustomImageView(
                          margin: EdgeInsets.all(5),
                          imagePath: AssetConstants.pngBack,
                          height: 18.0.h,
                        ),
                      ),
                      buildSizedBoxW(18.0),
                      Text(
                        'Location',
                        style: Theme.of(context)
                            .textTheme
                            .titleLarge
                            ?.copyWith(fontSize: 16.5.sp, fontWeight: FontWeight.w700),
                      ),
                      Spacer(),
                      CustomElevatedButton(
                        text: "Done",
                        height: 30.h,
                        width: 80.w,
                        onPressed: () {
                          Navigator.of(context).pop();
                          state.searchLocation.clear();
                        },
                      ),
                    ],
                  ),
                  buildSizedBoxH(20.0),
                  ValueListenableBuilder<TextEditingValue>(
                    valueListenable: searchlocationController,
                    builder: (context, value, child) {
                      return SizedBox(
                        child: FlowkarTextFormField(
                          hint: Lang.of(context).lbl_search,
                          hintText: "Search Location",
                          context: context,
                          controller: searchlocationController,
                          textStyle: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(fontSize: 16.sp, color: Theme.of(context).customColors.black),
                          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontSize: 16.sp,
                              color: Theme.of(context).customColors.greylite,
                              fontWeight: FontWeight.w500),
                          onChanged: (value) async {
                            if (value.trim().length >= 3) {
                              setState(
                                () {
                                  context.read<PostBloc>().add(
                                        SearchLocationEvent(searchtext: value.trim(), lat: "", long: ""),
                                      );
                                },
                              );
                            }
                          },
                          fillColor: ThemeData().customColors.fillcolor,
                          borderDecoration: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(10.r)),
                            borderSide: BorderSide.none,
                          ),
                          filled: true,
                          contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
                          prefixIcon: CustomImageView(
                            imagePath: AssetConstants.icSearch,
                            height: 10,
                            width: 10,
                            margin: EdgeInsets.all(15.0),
                          ),
                          suffixIcon: value.text.isNotEmpty
                              ? CustomImageView(
                                  imagePath: AssetConstants.icClose,
                                  height: 10.0.h,
                                  width: 10.0.w,
                                  margin: EdgeInsets.all(16.5),
                                  onTap: () {
                                    setState(() {
                                      locationController.clear();
                                      searchlocationController.clear();
                                      state.searchLocation.clear();
                                      fetchAndSearchLocation(context);
                                    });
                                  },
                                )
                              : null,
                        ),
                      );
                    },
                  ),
                  buildSizedBoxH(20.0),
                  // Expanded(
                  //     child: (state.searchLocationloading ||
                  //             (state.searchLocation.isEmpty && searchlocationController.text.isEmpty))
                  //         ? Center(child: LoadingAnimationWidget())
                  //         : searchlocationController.text.isNotEmpty && state.searchLocation.isEmpty
                  //             ? isLoading
                  //                 ? Center(child: LoadingAnimationWidget())
                  //                 : Center(
                  //                     child: ExceptionWidget(
                  //                       imagePath: AssetConstants.pngNoResultFound,
                  //                       title: "Please input valid location",
                  //                       subtitle: "",
                  //                       showButton: false,
                  //                     ),
                  //                   )
                  //             : ListView.builder(
                  //                 shrinkWrap: true,
                  //                 itemCount: state.searchLocation.length,
                  //                 itemBuilder: (context, index) {
                  //                   return Column(
                  //                     mainAxisSize: MainAxisSize.min,
                  //                     children: [
                  //                       GestureDetector(
                  //                         onTap: () {
                  //                           locationController.text =
                  //                               state.searchLocation[index].suggestedPlace.toString();
                  //                           searchlocationController.text =
                  //                               state.searchLocation[index].suggestedPlace.toString();
                  //                         },
                  //                         child: SizedBox(
                  //                           width: double.infinity,
                  //                           child: Text(
                  //                             overflow: TextOverflow.ellipsis,
                  //                             maxLines: 2,
                  //                             state.searchLocation[index].suggestedPlace.toString(),
                  //                           ),
                  //                         ),
                  //                       ),
                  //                       Divider(color: Colors.grey.shade300),
                  //                     ],
                  //                   );
                  //                 },
                  //               )),
                  Expanded(
                    child: (state.searchLocationloading ||
                            (state.searchLocation.isEmpty && searchlocationController.text.isEmpty))
                        ? Center(child: LoadingAnimationWidget())
                        : state.searchLocation.isEmpty
                            ? isLoading || state.isSearchLocationHasData
                                ? Center(child: LoadingAnimationWidget())
                                : ExceptionWidget(
                                    imagePath: AssetConstants.pngNoResultFound,
                                    title: "Please input valid location",
                                    subtitle: "",
                                    showButton: false,
                                  )
                            : ListView.builder(
                                shrinkWrap: true,
                                itemCount: state.searchLocation.length,
                                itemBuilder: (context, index) {
                                  return Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          locationController.text =
                                              state.searchLocation[index].suggestedPlace.toString();
                                          searchlocationController.text =
                                              state.searchLocation[index].suggestedPlace.toString();
                                          context.read<PostBloc>().add(
                                                SearchLocationEvent(
                                                    searchtext: state.searchLocation[index].suggestedPlace.toString(),
                                                    lat: "",
                                                    long: ""),
                                              );
                                          Navigator.of(context).pop();
                                        },
                                        child: SizedBox(
                                          width: double.infinity,
                                          child: Text(
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 2,
                                            state.searchLocation[index].suggestedPlace.toString(),
                                          ),
                                        ),
                                      ),
                                      Divider(color: Colors.grey.shade300),
                                    ],
                                  );
                                },
                              ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

// Upload Post Button
  Widget _buildPostButton(PostState state) {
    return Center(
      child: CustomElevatedButton(
        width: 100.w,
        height: 40.h,
        isDisabled: state.isUploading,
        isLoading: state.isUploading,
        onPressed: () {
          _uploadPost(isposts: true);
        },
        fontSize: 13.sp,
        brderRadius: 8.r,
        text: Lang.of(context).lbl_post_now,
      ),
    );
  }

  Widget _buildshareWith() {
    return Padding(
      padding: EdgeInsets.only(right: 0.0.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            Lang.of(context).lbl_share_with,
            style: Theme.of(context).textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w700, fontSize: 18.0.sp),
          ),
          TextButton(
              onPressed: _isSwitchEnabled
                  ? () {
                      setState(() {
                        _shareWith = !_shareWith;
                        _updatePlatformSwitches(_shareWith);
                      });
                    }
                  : null,
              child: Text(
                'Select all',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      fontSize: 12.0.sp,
                      color: !_shareWith
                          ? Theme.of(context).primaryColor.withOpacity(0.4)
                          : Theme.of(context).primaryColor,
                    ),
              )),
        ],
      ),
    );
  }

  bool allowsOnlyVideo(String platform) {
    return ["VIMEO", "YOUTUBE", "TIKTOK"].contains(platform);
  }

  bool allowsOnlyImage(String platform) {
    return ["PINTEREST"].contains(platform);
  }

  bool allowsBoth(String platform) {
    return ["FACEBOOK", "INSTAGRAM", "THREAD", "LINKEDIN", "TUMBLR", "REDDIT", "X", "MASTODON"].contains(platform);
  }

  bool allowsText(String platform) {
    return ["TELEGRAM"].contains(platform);
  }

  Widget _buildSocialConnectList(List<SocialPlatform> socialPlatforms) {
    return SingleChildScrollView(
      physics: NeverScrollableScrollPhysics(),
      child: Wrap(
        runSpacing: 14.h,
        children: List.generate(
          socialPlatforms.length,
          (index) {
            String platformName = socialPlatforms[index].platformName.toUpperCase();
            isSocailMediaConnected = socialPlatformsStatus.value[platformName] ?? false;
            isEnableAllSocialMedia = socialPlatforms.every(
              (element) => element.isEnabled == false,
            );

            if (!isSocailMediaConnected) {
              return SizedBox.shrink();
            }

            bool isSupported = false;

            if (isTextplatform) {
              isSupported = allowsText(platformName);
            } else if (isVideoplatform) {
              isSupported = allowsOnlyVideo(platformName) || allowsBoth(platformName);
            } else if (isImageplatform) {
              isSupported = allowsOnlyImage(platformName) || allowsBoth(platformName);
            } else if (isbothplatform) {
              isSupported = allowsBoth(platformName);
            }

            // Reset switch state if platform is not supported
            if (!isSupported && _platformSwitches[index]) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                setState(() {
                  _platformSwitches[index] = false;
                  _shareWith = _platformSwitches.contains(true);
                });
              });
            }

            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: SizedBox(
                height: 70.h,
                width: 70.w,
                child: InkWell(
                  onTap: isSupported
                      ? () {
                          setState(() {
                            _platformSwitches[index] = !_platformSwitches[index];
                            _shareWith = _platformSwitches.contains(true);
                            Logger.lOG('Platform $platformName switch changed to: ${_platformSwitches[index]}');
                          });
                        }
                      : () {
                          if (!allowsText(platformName)) {
                            showDialog(
                              context: context,
                              builder: (ctx) {
                                return CustomAlertDialog(
                                    width: 360.w,
                                    isredius: true,
                                    imageheight: 45.h,
                                    imagewidth: 45.w,
                                    fit: BoxFit.contain,
                                    singleButton: true,
                                    title: Lang.of(ctx).lbl_format_not_supported,
                                    subtitle:
                                        "${capitalizeFirstLetter(platformName)}${Lang.of(ctx).lbl_format_not_supported_discription}",
                                    onConfirmButtonPressed: () async {
                                      NavigatorService.goBack();
                                    },
                                    confirmButtonText: Lang.of(ctx).lbl_okay,
                                    isLoading: false);
                              },
                            );
                          } else {
                            showDialog(
                              context: context,
                              builder: (ctx) {
                                return CustomAlertDialog(
                                    width: 360.w,
                                    isredius: true,
                                    imageheight: 45.h,
                                    imagewidth: 45.w,
                                    fit: BoxFit.contain,
                                    singleButton: true,
                                    title: "Text Messages Only",
                                    subtitle:
                                        "${capitalizeFirstLetter(platformName)} supports text messages only. Images and videos cannot be shared on this platform.", // Different description for text platforms
                                    onConfirmButtonPressed: () async {
                                      NavigatorService.goBack();
                                    },
                                    confirmButtonText: Lang.of(ctx).lbl_okay,
                                    isLoading: false);
                              },
                            );
                          }
                        },
                  child: Container(
                    decoration: BoxDecoration(
                      color: _platformSwitches[index] && isSupported
                          ? Theme.of(context).primaryColor.withOpacity(0.4)
                          : Theme.of(context).customColors.uploadPostSocialBTN,
                      border: Border.all(
                          width: 2.w,
                          color: _platformSwitches[index] && isSupported
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).customColors.uploadPostSocialBTN),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: CustomImageView(
                      radius: BorderRadius.circular(8.r),
                      margin: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                      imagePath: socialPlatforms[index].icon,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  String capitalizeFirstLetter(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  // teg User List
  Widget _buildUserList(PostState state) {
    return MentionUserListWidget(
      userList: state.searchuserList,
      descriptionController: descriptionController,
      onMentionAdded: (List<Map<String, String>> mentionedUsers) {
        setState(() {
          for (var user in mentionedUsers) {
            bool isDuplicate = tagUserList.any((existingUser) => existingUser['user_id'] == user['user_id']);

            if (!isDuplicate) {
              tagUserList.add(user);
              NavigatorService.goBack();
              descriptionController.clear();
            }
          }
          Logger.lOG("FINAL TAG LIST: $tagUserList");
          state.searchuserList.clear();
        });
      },
    );
  }

  Widget _buildtegUserPost() {
    return GestureDetector(
      onTap: () {},
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
        ),
        width: double.infinity,
        child: Align(
          alignment: Alignment.centerLeft,
          child: Column(
            children: [
              Wrap(
                spacing: 8.0,
                runSpacing: 8.0,
                children: tagUserList.map((user) {
                  return Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: Theme.of(context).primaryColor),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          user['user_name']!,
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(fontSize: 14.sp, fontWeight: FontWeight.bold),
                        ),
                        buildSizedBoxW(4),
                        GestureDetector(
                          onTap: () => removeTag(user['user_id']!, user['user_name']!),
                          child: Icon(Icons.close, size: 16.sp, color: Theme.of(context).primaryColor),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _openSchedulePostCalendar(BuildContext context) async {
    DateTime initialDate = DateTime.now();
    DateTime maxAllowedDate = DateTime.now().add(const Duration(days: 30)); // 1 મહિના પછીની તારીખ બ્લોક કરો

    DateTime? selectedDate = await showDatePicker(
      helpText: "Schedule Date",
      barrierDismissible: true,
      initialDatePickerMode: DatePickerMode.day,
      context: context,
      initialDate: initialDate,
      firstDate: initialDate,
      lastDate: maxAllowedDate, // 1 મહિના પછીની તારીખ સિલેક્ટ ન થઈ શકે
      initialEntryMode: DatePickerEntryMode.calendarOnly,
      selectableDayPredicate: (DateTime date) {
        return date.isAfter(DateTime.now().subtract(const Duration(days: 1))) &&
            date.isBefore(maxAllowedDate.add(const Duration(days: 1))); // 1 મહિના પછીની તારીખને અવગણો
      },
      builder: (BuildContext context, Widget? child) {
        return child!;
      },
    );

    if (selectedDate != null) {
      // DateTime minimumAllowedTime = DateTime.now().add(const Duration(minutes: 15));
      // TimeOfDay minimumTimeOfDay = TimeOfDay.fromDateTime(minimumAllowedTime);

      TimeOfDay initialTime = TimeOfDay.fromDateTime(DateTime.now().add(const Duration(minutes: 30)));

      // bool isToday = selectedDate.year == DateTime.now().year &&
      //     selectedDate.month == DateTime.now().month &&
      //     selectedDate.day == DateTime.now().day;

      TimeOfDay? selectedTime = await showTimePicker(
        context: context,
        initialTime: initialTime,
        builder: (BuildContext context, Widget? child) {
          // return MediaQuery(
          //   data: MediaQuery.of(context).copyWith(
          //     alwaysUse24HourFormat: false,
          //   ),
          //   child: child!,
          // );
          final ThemeData baseTheme = Theme.of(context);
          return Theme(
            data: baseTheme.copyWith(
              timePickerTheme: TimePickerThemeData(
                dayPeriodColor: MaterialStateColor.resolveWith((states) {
                  if (states.contains(MaterialState.selected)) {
                    return baseTheme.colorScheme.primary; // selected BG
                  }
                  return Colors.white; // unselected BG
                }),
                dayPeriodTextColor: MaterialStateColor.resolveWith((states) {
                  if (states.contains(MaterialState.selected)) {
                    return Colors.white; // selected text
                  }
                  return Colors.black; // unselected text
                }),
                dayPeriodShape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            child: MediaQuery(
              data: MediaQuery.of(context).copyWith(
                alwaysUse24HourFormat: false,
              ),
              child: child!,
            ),
          );
        },
      );

      if (selectedTime != null) {
        DateTime finalDateTime = DateTime(
          selectedDate.year,
          selectedDate.month,
          selectedDate.day,
          selectedTime.hour,
          selectedTime.minute,
        );

        if (finalDateTime.isAfter(DateTime.now().add(const Duration(minutes: 15)))) {
          setState(() {
            _selectedDateTime = finalDateTime;
            scheduledTimeContentController.text = _selectedDateTime!.formatWithTime12();

            Logger.lOG("Date: $_selectedDateTime");
          });
        } else {
          toastification.show(
            type: ToastificationType.error,
            showProgressBar: false,
            description: Text(
              "Please select a time at least 15 minutes from now",
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 12.0.sp),
            ),
            autoCloseDuration: const Duration(seconds: 3),
          );
        }
      }
    }
  }

  void showAudienceSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildHeader(context),
              Divider(color: Theme.of(context).primaryColor.withOpacity(0.1)),
              buildSizedBoxH(8),
              _buildDescription(context),
              buildSizedBoxH(24),
              _buildAudienceOption(
                context,
                title: 'Private',
                imagePath: Assets.images.svg.other.svgPrivate.path,
                value: true,
                isSelected: isPrivate,
                onTap: () => setState(() => isPrivate = true),
              ),
              _buildAudienceOption(
                context,
                title: 'Everyone',
                imagePath: Assets.images.svg.other.svgEveryone.path,
                value: false,
                isSelected: isPrivate,
                onTap: () => setState(() => isPrivate = false),
              ),
              buildSizedBoxH(24),
              _buildDoneButton(context),
              buildSizedBoxH(16.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Text(
        'Audience',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Text(
      'Who would you like to share your post with?',
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: 12.sp,
            color: Colors.black87,
            fontWeight: FontWeight.w700,
          ),
    );
  }

  Widget _buildAudienceOption(
    BuildContext context, {
    required String title,
    required String imagePath,
    required bool value,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            CustomImageView(imagePath: imagePath),
            buildSizedBoxW(16),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const Spacer(),
            Radio(
              value: value,
              groupValue: isSelected,
              onChanged: (_) => onTap(),
              activeColor: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDoneButton(BuildContext context) {
    return CustomElevatedButton(
      width: 100.w,
      text: 'Done',
      onPressed: () {
        Logger.lOG('Selected: ${isPrivate ? "Private" : "Everyone"}');
        Navigator.pop(context);
      },
    );
  }
}

class SocialPlatform {
  final String platformName;
  final String icon;
  final VoidCallback onTap;
  final bool isEnabled;

  SocialPlatform({
    required this.platformName,
    required this.icon,
    required this.onTap,
    required this.isEnabled,
  });
}

class VideoItem {
  final String path;
  final String name;
  final int totalChunks;
  final List<String> chunkPaths;

  VideoItem({required this.path, required this.name, required this.totalChunks, List<String>? chunkPaths})
      : chunkPaths = chunkPaths ?? [];

  // ✅ Convert object to JSON format
  Map<String, dynamic> toJson() {
    return {
      'path': path,
      'name': name,
      'totalChunks': totalChunks,
      'chunkPaths': chunkPaths,
    };
  }
}
