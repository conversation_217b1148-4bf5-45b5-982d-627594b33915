// ignore_for_file: deprecated_member_use

import 'dart:async';
import 'dart:math';

import 'package:detectable_text_field/widgets/detectable_text_editing_controller.dart';
import 'package:detectable_text_field/widgets/detectable_text_field.dart';
import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/home_feed_screen/model/analytics_model.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/caption_preview_widget.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_player.dart';
import 'package:cron/cron.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/share_post_bottomsheet.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/shimmer/analytics_shimmer.dart';
import 'package:flowkar/features/notification/bloc/notification_bloc.dart';
import 'package:flowkar/features/reels_screen/presentation/pages/video_page.dart';
import 'package:flowkar/features/reels_screen/service/reel_service.dart';
// import 'package:flowkar/features/reels_screen/presentation/pages/video_page.dart';
// import 'package:flowkar/features/reels_screen/service/reel_service.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flowkar/features/upload_post/bloc/post_bloc.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:intl/intl.dart' as intl;
import 'package:widget_zoom/widget_zoom.dart';
import 'package:zoom_pinch_overlay/zoom_pinch_overlay.dart';

// ignore: must_be_immutable
class PostWidget extends StatefulWidget {
  final HomeFeedState state;
  final bool isVideo;
  final bool isPost;
  final String profileImage;
  final List<String> thumbnailImage;
  final bool userpost;
  final bool userByIDpost;
  final bool userVideo;
  final bool userByIDvideo;
  // final List<AnalyticsData> analytics;
  final int index;
  final int postId;
  final int? userId;
  final String name;
  final String username;
  final List<String> postMedia;
  final String caption;
  final String title;
  String likes;
  final String comments;
  bool isLiked;
  final bool isSaved;
  final VoidCallback likeonTap;
  final VoidCallback doubleTap;
  final VoidCallback commentonTap;
  final VoidCallback shareonTap;
  final String postTime;
  // final HomeFeedState? homeFeedState;
  final String? reelScreenType;
  final String? screenType;
  final VoidCallback saveonTap;
  final String latestcomments;
  final bool? editePost;
  final VoidCallback? editpostOnTap;
  final bool isanalytics;
  final dynamic taggedIn;
  final int width;
  final int height;
  final bool? isprofilrpostDelete;
  final String? location;
  final bool isTextPost;
  final bool? userById;
  final bool? isNotificationPost;
  final bool? isDiscoverPosts;

  PostWidget({
    super.key,
    this.isVideo = false,
    this.isPost = false,
    required this.postId,
    required this.userId,
    required this.userpost,
    required this.userByIDpost,
    required this.userVideo,
    required this.userByIDvideo,
    required this.profileImage,
    required this.thumbnailImage,
    required this.name,
    required this.username,
    required this.postMedia,
    required this.caption,
    required this.title,
    required this.likes,
    required this.comments,
    required this.isLiked,
    required this.likeonTap,
    required this.doubleTap,
    required this.commentonTap,
    required this.shareonTap,
    required this.index,
    required this.postTime,
    required this.isSaved,
    this.screenType,
    this.reelScreenType,
    required this.latestcomments,
    required this.saveonTap,
    this.editePost = false,
    this.editpostOnTap,
    required this.state,
    this.isanalytics = false,
    required this.taggedIn,
    required this.width,
    required this.height,
    this.isprofilrpostDelete = false,
    this.location,
    this.isTextPost = false,
    this.userById = false,
    this.isNotificationPost = false,
    this.isDiscoverPosts = false,
  });
  static int? currentlyVisibleAnalyticsPostId;

  @override
  State<PostWidget> createState() => _PostWidgetState();
}

class _PostWidgetState extends State<PostWidget> with SingleTickerProviderStateMixin {
  final PageController _postcontroller = PageController();
  late FlickMultiManager flickMultiManager;
  bool _isindicatorVisible = true;
  int _currentMediaIndex = 0;
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _isAnimationVisible = false;
  late Cron _cron;
  bool _isAnalyticsVisible = false;
  String currentUserId = '';
  bool _isTextExpanded = false;
  bool _hasTextOverflow = false;
  double _textPostHeight = 80.h; // Default height
  DetectableTextEditingController captionController = DetectableTextEditingController();
  // PostData? postData;
  // String screen = '';
  late bool isPostPermission;
  late bool isBlockPermission;
  late bool isMessagePermission;

  final ReelService reelService = ReelService.instance;

  void _calculateTextHeight(BuildContext context) {
    if (widget.isTextPost) {
      // Calculate title height
      final TextSpan titleSpan = TextSpan(
        text: widget.title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontSize: 16.sp,
              fontWeight: FontWeight.w700,
            ),
      );

      final TextPainter titlePainter = TextPainter(
        text: titleSpan,
        textDirection: TextDirection.ltr,
      )..layout(maxWidth: MediaQuery.of(context).size.width - 56.w);

      // Calculate caption height
      final TextSpan captionSpan = TextSpan(
        text: widget.caption,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
      );

      final TextPainter captionPainter = TextPainter(
        text: captionSpan,
        textDirection: TextDirection.ltr,
      )..layout(maxWidth: MediaQuery.of(context).size.width - 56.w);

      // Calculate total content height
      final titleHeight = titlePainter.height;
      final captionHeight = captionPainter.height;
      final spacingHeight = 8.h; // Space between title and caption
      final paddingHeight = 20.w; // Container padding (top + bottom)
      final seeMoreHeight = 0.h; // Height for See More/Less button
      final bottomSpacing = 0.h; // Bottom spacing

      final totalContentHeight =
          titleHeight + spacingHeight + captionHeight + paddingHeight + seeMoreHeight + bottomSpacing;

      // Determine container height and overflow status
      final baseHeight = 200.h; // Base height threshold

      if (!_isTextExpanded) {
        // Collapsed state logic
        if (totalContentHeight <= baseHeight) {
          // Content fits within base height, use actual content height
          _textPostHeight = totalContentHeight;
          _hasTextOverflow = false;
        } else {
          // Content exceeds base height, limit to base height and show "See More"
          _textPostHeight = baseHeight;
          _hasTextOverflow = true;
        }
      } else {
        // Expanded state - use actual content height without scrolling
        _textPostHeight = totalContentHeight;
      }
    }
  }

  @override
  void initState() {
    super.initState();
    separatePostFromDifferentScreen();
    flickMultiManager = FlickMultiManager();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _controller.reverse();
        }
      });
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    _cron = Cron();
    _startAutoHide();
    currentUserId = Prefobj.preferences!.get(Prefkeys.USER_ID).toString();
  }

  void _startAutoHide() {
    _cron.schedule(Schedule.parse('*/10 * * * * *'), () {
      if (_isindicatorVisible) {
        setState(() {
          _isindicatorVisible = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _cron.close();
    super.dispose();
  }

  void _resetHideTimer() {
    _cron.close();
    _cron = Cron();
    _startAutoHide();
    // setState(() {
    _isindicatorVisible = true;
    // });
  }

  PostData? separatePostFromDifferentScreen() {
    switch (widget.reelScreenType) {
      case "HomeFeed":
        return PostData.fromJson(widget.state.posts[widget.index].toJson());

      case "Profile":
        return PostData(
            id: widget.state.getProfilePost[widget.index].id,
            title: widget.state.getProfilePost[widget.index].title,
            description: widget.state.getProfilePost[widget.index].description,
            location: widget.state.getProfilePost[widget.index].location,
            likes: widget.state.getProfilePost[widget.index].likes,
            dislikes: widget.state.getProfilePost[widget.index].dislikes,
            commentsCount: widget.state.getProfilePost[widget.index].commentsCount,
            createdAt: widget.state.getProfilePost[widget.index].createdAt,
            scheduledAt: widget.state.getProfilePost[widget.index].scheduledAt,
            files: widget.state.getProfilePost[widget.index].files,
            thumbnailFiles: widget.state.getProfilePost[widget.index].thumbnailFiles,
            latestComment: widget.state.getProfilePost[widget.index].latestComment,
            user: User.fromJson(widget.state.getProfilePost[widget.index].user.toJson()),
            isLiked: widget.state.getProfilePost[widget.index].isLiked,
            isSaved: widget.state.getProfilePost[widget.index].isSaved,
            width: widget.state.getProfilePost[widget.index].width,
            height: widget.state.getProfilePost[widget.index].height,
            isTextPost: false);

      case "Notification":
        return PostData(
            id: widget.state.getpostbyIdData[widget.index].id ?? 0,
            title: widget.state.getpostbyIdData[widget.index].title ?? '',
            description: widget.state.getpostbyIdData[widget.index].description ?? '',
            location: widget.state.getpostbyIdData[widget.index].location ?? '',
            likes: widget.state.getpostbyIdData[widget.index].likes ?? 0,
            dislikes: widget.state.getpostbyIdData[widget.index].dislikes ?? 0,
            commentsCount: widget.state.getpostbyIdData[widget.index].commentsCount ?? 0,
            createdAt: widget.state.getpostbyIdData[widget.index].createdAt ?? '',
            scheduledAt: "",
            files: widget.state.getpostbyIdData[widget.index].files ?? [],
            thumbnailFiles: widget.state.getpostbyIdData[widget.index].files ?? [],
            latestComment: widget.state.getpostbyIdData[widget.index].latestComment ?? '',
            user: User.fromJson(widget.state.getpostbyIdData[widget.index].user?.toJson() ?? {}),
            isLiked: widget.state.getpostbyIdData[widget.index].isLiked ?? false,
            isSaved: widget.state.getpostbyIdData[widget.index].isSaved ?? false,
            width: widget.state.getpostbyIdData[widget.index].width ?? 0,
            height: widget.state.getpostbyIdData[widget.index].height ?? 0,
            isTextPost: false);

      case "Discover":
        return PostData.fromJson(widget.state.isDiscoverposts[widget.index].toJson());
      case "Follower":
        try {
          Logger.lOG("widget.postdata Before ${widget.state.isuserProfileposts[widget.index].id}");
          return PostData.fromJson(widget.state.isuserProfileposts[widget.index].toJson());
        } catch (e) {
          Logger.lOG("Error: $e");
        }

        Logger.lOG("widget.postdata Before in Profile Photoo ${widget.state.getProfileByIDvideo[widget.index].id}");
        break;
      default:
        return null;
    }
    return null;
  }

  void toggleAnalytics() {
    setState(() {
      // If this post's analytics are currently visible
      if (_isAnalyticsVisible) {
        _isAnalyticsVisible = false;
        PostWidget.currentlyVisibleAnalyticsPostId = null;
      } else {
        // If another post's analytics are visible, we need to trigger a rebuild
        // of that widget to hide its analytics
        if (PostWidget.currentlyVisibleAnalyticsPostId != null) {
          // This will force a rebuild of all PostWidget instances
          context.findAncestorStateOfType<_PostWidgetState>()?.setState(() {});
        }
        _isAnalyticsVisible = true;
        PostWidget.currentlyVisibleAnalyticsPostId = widget.postId;
      }

      if (_isAnalyticsVisible) {
        context.read<HomeFeedBloc>().add(AnalyticsApiEvent(postId: widget.postId));
      }
    });
  }

  @override
  void didUpdateWidget(PostWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_isAnalyticsVisible && PostWidget.currentlyVisibleAnalyticsPostId != widget.postId) {
      setState(() {
        _isAnalyticsVisible = false;
      });
    }
    if (widget.caption != oldWidget.caption || _isTextExpanded != (_isTextExpanded)) {
      _calculateTextHeight(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    isPostPermission = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    isBlockPermission = Prefobj.preferences?.get(Prefkeys.PRM_BLOCK_UNBLOCK) ?? false;
    isMessagePermission = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;

    _calculateTextHeight(context);
    double pixelWidth = widget.width.toDouble();
    double pixelHeight = widget.height.toDouble();
    double aspectRatio = pixelWidth / pixelHeight;

    double deviceWidth = MediaQuery.of(context).size.width;

    double maxHeight = 450.0;

    double calculatedWidth = min(deviceWidth, deviceWidth);

    double calculatedHeight = calculatedWidth / aspectRatio;

    if (calculatedHeight > maxHeight) {
      calculatedHeight = maxHeight;
      calculatedWidth = calculatedHeight * aspectRatio;
    }

    return Padding(
      padding: EdgeInsets.only(bottom: 15.0.h, top: 1.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildUserDetail(context),
          Stack(
            children: [
              Column(
                children: [
                  _buildPostMedia(context),
                  widget.editePost == true ? const SizedBox.shrink() : _buildPostDetail(context),
                  // buildSizedBoxH(16),
                  // if (widget.userId.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID))
                  //   _isAnalyticsVisible
                  //       ? _buildExpandedAnalyticsList(context, widget.state.analyticsModel)
                  //       : const SizedBox.shrink(),
                ],
              ),
              if (widget.editePost != true)
                Positioned(
                  top: widget.isTextPost ? _textPostHeight - 26.h : min(calculatedHeight - 26.h, 440.h - 26.h),
                  left: 18.w,
                  child: Material(
                    color: Colors.transparent,
                    child: _buildLikeCommentShare(),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTextPostContainer(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 18.w),
      padding: EdgeInsets.all(10.w),
      height: _textPostHeight,
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.white,
        // borderRadius: BorderRadius.only(bottomLeft: Radius.circular(20.r), bottomRight: Radius.circular(20.r)),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).customColors.black.withOpacity(0.2),
            blurRadius: 2,
            offset: const Offset(0, 1),
          )
        ],
      ),
      child: _isTextExpanded ? _buildExpandedTextContent(context) : _buildCollapsedTextContent(context),
    );
  }

  Widget _buildCollapsedTextContent(BuildContext context) {
    final availableHeight = _textPostHeight - 20.h - 26.h;
    final availableForCaption = availableHeight - 8.h - (_hasTextOverflow ? 20.h : 0);

    final lineHeight = 18.h;
    final maxLines = (availableForCaption / lineHeight).floor().clamp(2, 100);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildRichText(context, maxLines),
        if (_hasTextOverflow) ...[
          buildSizedBoxH(4),
          Align(
            alignment: Alignment.centerRight,
            child: InkWell(
              onTap: () {
                setState(() {
                  _isTextExpanded = true;
                });
              },
              child: Text(
                'See More',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).primaryColor,
                    ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildExpandedTextContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildRichText(context, null),
        buildSizedBoxH(4),
        Align(
          alignment: Alignment.centerRight,
          child: InkWell(
            onTap: () {
              setState(() {
                _isTextExpanded = false;
              });
            },
            child: Text(
              'See Less',
              style: Theme.of(context)
                  .textTheme
                  .labelSmall
                  ?.copyWith(fontWeight: FontWeight.w600, color: Theme.of(context).primaryColor),
            ),
          ),
        ),
      ],
    );
  }

// Helper method to build RichText with bold title extracted from caption
  // Widget _buildRichText(BuildContext context, int? maxLines) {
  //   List<String> captionParts = widget.caption.split('\n');

  //   String title = '';
  //   String description = '';

  //   if (captionParts.isNotEmpty) {
  //     title = captionParts[0];
  //     if (captionParts.length > 1) {
  //       description = captionParts.sublist(1).join('\n');
  //     }
  //   }

  //   List<TextSpan> spans = [];
  //   if (title.isNotEmpty) {
  //     spans.add(
  //       TextSpan(
  //         text: title,
  //         style: Theme.of(context)
  //             .textTheme
  //             .bodyLarge
  //             ?.copyWith(fontSize: 13.sp, fontWeight: FontWeight.bold),
  //       ),
  //     );
  //   }

  //   if (description.isNotEmpty) {
  //     if (title.isNotEmpty) {
  //       spans.add(
  //         TextSpan(
  //           text: '\n',
  //           style: Theme.of(context)
  //               .textTheme
  //               .bodyLarge
  //               ?.copyWith(fontSize: 12.sp, fontWeight: FontWeight.w500),
  //         ),
  //       );
  //     }

  //     spans.add(
  //       TextSpan(
  //         text: description,
  //         style: Theme.of(context).textTheme.bodyLarge?.copyWith(
  //               fontSize: 14.sp,
  //               fontWeight: FontWeight.w500,
  //             ),
  //       ),
  //     );
  //   }

  //   return RichText(
  //     text: TextSpan(children: spans),
  //     maxLines: maxLines,
  //     overflow: maxLines != null ? TextOverflow.ellipsis : TextOverflow.visible,
  //   );
  // }
  Widget _buildRichText(BuildContext context, int? maxLines) {
    List<String> captionParts = widget.caption.split('\n');

    String title = '';
    String description = '';

    if (captionParts.isNotEmpty) {
      title = captionParts[0];
      if (captionParts.length > 1) {
        description = captionParts.sublist(1).join('\n');
      }
    }

    List<TextSpan> spans = [];

    if (title.isNotEmpty) {
      spans.add(
        TextSpan(
          text: title,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 13.sp, fontWeight: FontWeight.bold),
        ),
      );
    }

    if (description.isNotEmpty) {
      if (title.isNotEmpty) {
        spans.add(
          TextSpan(
            text: '\n',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 12.sp, fontWeight: FontWeight.w500),
          ),
        );
      }

      /// 🔽 Changed Part: Parse and make hashtags clickable
      final regex = RegExp(r'(\#[\w\d_]+)|([^\#\s]+|\s+)', multiLine: true);
      final matches = regex.allMatches(description);

      for (final match in matches) {
        final text = match.group(0)!;
        final isTag = text.startsWith('#');

        spans.add(
          TextSpan(
            text: text,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontSize: 14.sp,
                  fontWeight: isTag ? FontWeight.bold : FontWeight.w500,
                  color: isTag ? Theme.of(context).primaryColor : null,
                ),
            recognizer: isTag
                ? (TapGestureRecognizer()
                  ..onTap = () {
                    Logger.lOG('Clicked tag: $text');
                    // Or use Get.snackbar / Toast if needed
                  })
                : null,
          ),
        );
      }
    }

    return RichText(
      text: TextSpan(children: spans),
      maxLines: maxLines,
      overflow: maxLines != null ? TextOverflow.ellipsis : TextOverflow.visible,
    );
  }

// MARK: User Detail
  Widget _buildUserDetail(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).customColors.black.withOpacity(0.2),
              blurRadius: 2,
            )
          ],
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          )),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Row(
              // mainAxisSize: MainAxisSize.min,
              children: [
                ClipRRect(
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Container(
                      height: 43.0.h,
                      width: 43.0.w,
                      padding: (widget.profileImage == AssetConstants.pngUser ||
                                  widget.profileImage.isEmpty ||
                                  widget.profileImage == '') &&
                              widget.userId.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                          ? EdgeInsets.all(8)
                          : EdgeInsets.all(2),
                      // padding: widget.profileImage.isEmpty ? EdgeInsets.all(10) : EdgeInsets.zero,
                      decoration: BoxDecoration(
                          border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.2), width: 2),
                          // borderRadius: BorderRadius.circular(100.r),
                          shape: BoxShape.circle),
                      child: CustomImageView(
                        onTap: () {
                          if (widget.userId.toString() == currentUserId) {
                            PersistentNavBarNavigator.pushNewScreen(context,
                                screen: UserProfileScreen(), withNavBar: false);

                            // NavigatorService.pushNamed(AppRoutes.userProfileScreen);
                          } else {
                            PersistentNavBarNavigator.pushNewScreen(context,
                                screen: GetUserProfileById(userId: widget.userId, stackonScreen: false),
                                withNavBar: false);
                          }
                        },
                        radius: widget.userId.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                            ? widget.profileImage.isEmpty
                                ? null
                                : BorderRadius.circular(100.0)
                            : BorderRadius.circular(100.0),
                        // radius: widget.profileImage.isEmpty ? null : BorderRadius.circular(100.r),
                        fit: (widget.profileImage == AssetConstants.pngUser ||
                                widget.profileImage.isEmpty ||
                                widget.profileImage == '')
                            ? BoxFit.contain
                            : BoxFit.cover,
                        imagePath: widget.profileImage.isEmpty
                            ? widget.userId.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                ? AssetConstants.pngUser
                                : AssetConstants.pngUserReomve
                            : widget.profileImage,
                        fallbackImage: AssetConstants.pngUserReomve,
                        // alignment: (widget.profileImage == AssetConstants.pngUser) ? Alignment.center : null,
                      ),
                    ),
                  ),
                ),
                buildSizedBoxW(8),
                Expanded(
                  child: InkWell(
                    onTap: () {
                      if (widget.userId.toString() == currentUserId) {
                        PersistentNavBarNavigator.pushNewScreen(context,
                            screen: UserProfileScreen(), withNavBar: false);
                        // NavigatorService.pushNamed(AppRoutes.userProfileScreen);
                      } else {
                        PersistentNavBarNavigator.pushNewScreen(context,
                            screen: GetUserProfileById(userId: widget.userId, stackonScreen: false), withNavBar: false);
                      }
                    },
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.name,
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge!
                              .copyWith(fontSize: 15.sp, fontWeight: FontWeight.w700, color: Color(0xff292D32)),
                        ),
                        buildSizedBoxH(1),
                        Text(
                          "@${widget.username}",
                          style: Theme.of(context)
                              .textTheme
                              .headlineSmall!
                              .copyWith(fontSize: 12.sp, fontWeight: FontWeight.w700, color: Color(0xff575353)),
                        ),
                        if (widget.location != null && widget.location!.isNotEmpty)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              buildSizedBoxH(4),
                              Row(
                                children: [
                                  Icon(Icons.location_on, size: 11.sp, color: Theme.of(context).primaryColor),
                                  Expanded(
                                    child: Text(
                                      widget.location ?? "",
                                      overflow: TextOverflow.ellipsis,
                                      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                                          fontSize: 10.sp, fontWeight: FontWeight.w600, color: Color(0xff575353)),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
          widget.editePost == true
              ? const SizedBox.shrink()
              : TextButton(
                  style: ButtonStyle(overlayColor: WidgetStatePropertyAll(Colors.transparent)),
                  onPressed: widget.isanalytics == true
                      ? null
                      : (isPostPermission || isBlockPermission)
                          ? () {
                              // if (widget.userId == PrefObj.preferences?.get((Prefkeys.USER_ID))) {
                              //   _builduserMoreOptionBottomSheet(context, themestate);
                              // } else {
                              _buildMoreOptionBottomSheet(context);
                              // }
                            }
                          : () {
                              showToastNoPermission(access: "");
                            },
                  child: widget.isanalytics == true
                      ? SizedBox.shrink()
                      : SizedBox(
                          width: 20.w,
                          child: CustomImageView(
                            imagePath: Assets.images.icons.other.icMore.path,
                            // height: 25.h,
                          ),
                        ),
                )
          // : SizedBox.shrink()
        ],
      ),
    );
  }

  bool istagUsershow = false;
  List<TaggedUser> taggedUsers = [];

// MARK: Fetch Tagged User Names
  Future<void> fetchTaggedUserNames(BuildContext context, dynamic taggedIn) async {
    if (taggedIn == null || (taggedIn is Map && taggedIn.isEmpty) || (taggedIn is List && taggedIn.isEmpty)) {
      Logger.lOG("Invalid or empty tagged list.");
      return;
    }

    final completer = Completer<void>();
    int expectedCount = taggedIn.length;
    int receivedCount = 0;

    if (taggedIn is List) {
      taggedUsers.clear();
      for (var id in taggedIn) {
        String userId = id.toString();

        SocketService.emit(
            APIConfig.getUserName, {'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN), 'id': userId});

        SocketService.response(APIConfig.getUserName, (response) {
          Logger.lOG("Response from API for ID $userId: $response");

          if (response != null && response is Map && response.containsKey('username')) {
            String userName = response['username'];
            int tagUserId = response['id'];
            String profileImage = response['profile_picture'];

            taggedUsers.add(TaggedUser(username: userName, id: tagUserId, profileImage: profileImage));
            receivedCount++;

            if (receivedCount == expectedCount) {
              setState(() {});
              if (!completer.isCompleted) {
                completer.complete();
              }
            }
          } else {
            Logger.lOG("No valid user name found for ID $userId.");
            receivedCount++;
            if (receivedCount == expectedCount && !completer.isCompleted) {
              completer.complete();
            }
          }
        });
      }
    }

    return completer.future;
  }

// MARK: Post Media
  Widget _buildPostMedia(BuildContext context) {
    // Logger.lOG("widget.height!: ${widget.height} \n${widget.height?.toDouble() ?? 0.0 / MediaQuery.of(context).devicePixelRatio}");
    // Logger.lOG("widget.width: ${widget.width}  \n${widget.width?.toDouble() ?? 0.0 / MediaQuery.of(context).devicePixelRatio}");
    double pixelWidth = widget.width.toDouble();
    double pixelHeight = widget.height.toDouble();
    double aspectRatio = pixelWidth / pixelHeight;

    double deviceWidth = MediaQuery.of(context).size.width;

    double maxHeight = 450.0;

    double calculatedWidth = min(deviceWidth, deviceWidth);

    double calculatedHeight = calculatedWidth / aspectRatio;

    if (calculatedHeight > maxHeight) {
      calculatedHeight = maxHeight;
      calculatedWidth = calculatedHeight * aspectRatio;
    }
    // double aspectRatio = pixelWidth / pixelHeight;
    return InkWell(
      onDoubleTap: () {
        _onDoubleTap();
        if (!widget.isLiked) {
          widget.isLiked = true;
          widget.likes = (int.parse(widget.likes) + 1).toString();
        }
        widget.doubleTap();
      },
      child: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.center,
        children: [
          widget.isTextPost
              ? _buildTextPostContainer(context)
              : ConstrainedBox(
                  constraints: BoxConstraints(
                      maxHeight: 440.h, maxWidth: MediaQuery.of(context).size.width, minWidth: calculatedWidth),
                  child:
                      // AspectRatio(
                      //   aspectRatio: aspectRatio,
                      //   child:
                      SizedBox(
                    //  width: widget.width! / MediaQuery.of(context).devicePixelRatio,
                    // height: MediaQuery.of(context).size.width / aspectRatio,
                    height: calculatedHeight,
                    // width: calculatedWidth,
                    child: PageView.builder(
                      controller: _postcontroller,
                      onPageChanged: (index) {
                        setState(() {
                          _currentMediaIndex = index;
                          _resetHideTimer();
                        });
                      },
                      itemCount: widget.postMedia.length,
                      itemBuilder: (context, index) {
                        final mediaUrl = widget.postMedia[index];

                        return isVideo(mediaUrl)
                            ? GestureDetector(
                                onTap: () {
                                  // final post = separatePostFromDifferentScreen();
                                  // // Logger.lOG("Click ${post?.id ?? ''},    $index ,");
                                  // if (post == null) {
                                  //   Logger.lOG("Post is null. Not navigating.");
                                  //   return;
                                  // }
                                  try {
                                    FocusManager.instance.primaryFocus?.unfocus();
                                    PersistentNavBarNavigator.pushNewScreen(context,
                                        screen: VideoReelPage(
                                            index: 0,
                                            reelService: ReelService(),
                                            screen: 'HomeFeed',
                                            postdata: widget.userpost
                                                ? widget.state.getProfilePost[widget.index]
                                                : widget.userByIDpost
                                                    ? widget.state.isuserProfileposts[widget.index]
                                                    : widget.state.posts[widget.index]),
                                        withNavBar: false);
                                  } catch (e) {
                                    Logger.lOG("Error: $e");
                                  }
                                  // Logger.lOG("widget.postdata Before ${postData!.id}");
                                },
                                child: FlickMultiPlayer(
                                  key: ObjectKey(mediaUrl),
                                  url: mediaUrl,
                                  flickMultiManager: flickMultiManager,
                                  image:
                                      (widget.thumbnailImage.length > index && widget.thumbnailImage[index].isNotEmpty)
                                          ? widget.thumbnailImage[index]
                                          : AssetConstants.pngPlaceholder,
                                ),
                              )
                            : ZoomOverlay(
                                // modalBarrierColor: Colors.black.withOpacity(0.8),
                                // minScale: 1.0,
                                // maxScale: 3.0,
                                twoTouchOnly: true, // only zoom with 2 fingers
                                animationDuration: Duration(milliseconds: 300),
                                child: AspectRatio(
                                  aspectRatio: aspectRatio,
                                  child: CustomImageView(
                                    imagePath: mediaUrl,
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                  ),
                                ),
                              );
                        // WidgetZoom(
                        //     heroAnimationTag: "post",
                        //     zoomWidget: AspectRatio(
                        //       aspectRatio: aspectRatio,
                        //       child: CustomImageView(
                        //         imagePath: mediaUrl,
                        //         fit: BoxFit.cover,
                        //         width: double.infinity,
                        //       ),
                        //     ),
                        //   );
                      },
                    ),
                  ),
                ),
          // SizedBox(
          //   height: 310.h,
          //   child: PageView.builder(
          //     controller: _postcontroller,
          //     onPageChanged: (index) {
          //       setState(() {
          //         _currentMediaIndex = index;
          //         _resetHideTimer();
          //       });
          //     },
          //     itemCount: widget.postMedia.length,
          //     itemBuilder: (context, index) {
          //       final mediaUrl = widget.postMedia[index];
          //       return isVideo(mediaUrl)
          //           ? FlickMultiPlayer(
          //               key: ObjectKey(mediaUrl),
          //               url: mediaUrl,
          //               flickMultiManager: flickMultiManager,
          //               image: (widget.thumbnailImage.length > index && widget.thumbnailImage[index].isNotEmpty) ? widget.thumbnailImage[index] : AssetConstants.pngPlaceholder,
          //             )
          //           : CustomImageView(
          //               imagePath: mediaUrl,
          //               fit: BoxFit.cover,
          //               width: double.infinity,
          //             );
          //     },
          //   ),
          // ),
          if (_isAnimationVisible)
            Center(
              child: AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _animation.value,
                    child: Icon(
                      Icons.favorite_rounded,
                      size: widget.isTextPost ? 70.h : 100.h,
                      color: Colors.red.withOpacity(0.7),
                    ),
                  );
                },
              ),
            ),
          if (widget.postMedia.length > 1)
            _isindicatorVisible
                ? Positioned(
                    right: 18.w,
                    top: 10,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                      decoration: BoxDecoration(
                        color: Colors.black38,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Text(
                        '${_currentMediaIndex + 1}/${widget.postMedia.length}',
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: Theme.of(context).customColors.white, fontSize: 10.0.sp),
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          if (widget.taggedIn is List && widget.taggedIn.isNotEmpty)
            Positioned(
              left: widget.isTextPost ? 10.w : 6.w,
              bottom: 20.h,
              child: IconButton(
                onPressed: _handleTaggedUsers,
                icon: Container(
                  height: 28.h,
                  width: 28.w,
                  decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.black87),
                  child: Icon(Icons.person, color: Colors.white, size: 18.sp),
                ),
              ),
            ),
          if (istagUsershow)
            Positioned(
              left: 54.w,
              bottom: 40.h,
              child: Container(
                width: MediaQuery.of(context).size.width - 80.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      spacing: 5.w,
                      runSpacing: 5.h,
                      children: taggedUsers.map((taggedUser) {
                        return GestureDetector(
                          onTap: () {
                            Logger.lOG('Tapped user ID: ${taggedUser.id}');

                            if (taggedUser.id.toString() == currentUserId) {
                              PersistentNavBarNavigator.pushNewScreen(context,
                                  screen: UserProfileScreen(), withNavBar: false);
                            } else {
                              PersistentNavBarNavigator.pushNewScreen(context,
                                  screen: GetUserProfileById(userId: widget.userId, stackonScreen: false),
                                  withNavBar: false);
                            }
                          },
                          child: Container(
                            padding: EdgeInsets.all(5),
                            decoration: BoxDecoration(
                              color: Colors.black45,
                              borderRadius: BorderRadius.circular(5.r),
                            ),
                            child: Text(
                              "@${taggedUser.username}",
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(fontSize: 12.sp, color: Theme.of(context).customColors.white),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            )
        ],
      ),
    );
  }

  void _handleTaggedUsers() async {
    if (widget.taggedIn == null || (widget.taggedIn is List && widget.taggedIn.isEmpty)) {
      return;
    }

    try {
      await fetchTaggedUserNames(context, widget.taggedIn);

      if (taggedUsers.length > 5) {
        showModalBottomSheet(
          // ignore: use_build_context_synchronously
          context: context,
          backgroundColor: Colors.transparent,
          isScrollControlled: true,
          useRootNavigator: true,
          builder: (_) {
            return DraggableScrollableSheet(
              initialChildSize: 0.4,
              minChildSize: 0.2,
              maxChildSize: 0.8,
              builder: (context, scrollController) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  child: Column(
                    children: [
                      buildSizedBoxH(13),
                      Container(
                        width: 50.0.w,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Theme.of(context).customColors.bottomsheetHendalColor,
                            width: 3,
                          ),
                          borderRadius: BorderRadius.circular(100.r),
                        ),
                      ),
                      buildSizedBoxH(17),
                      Expanded(
                        child: ListView.builder(
                          controller: scrollController,
                          itemCount: taggedUsers.length,
                          itemBuilder: (context, index) {
                            final taggedUser = taggedUsers[index];
                            return InkWell(
                              onTap: () {
                                if (taggedUser.id.toString() == currentUserId) {
                                  PersistentNavBarNavigator.pushNewScreen(
                                    context,
                                    screen: UserProfileScreen(),
                                    withNavBar: false,
                                  );
                                } else {
                                  PersistentNavBarNavigator.pushNewScreen(
                                    context,
                                    screen: GetUserProfileById(userId: taggedUser.id, stackonScreen: false),
                                    withNavBar: false,
                                  );
                                }
                              },
                              child: Container(
                                margin: EdgeInsets.only(bottom: 10.h),
                                child: Row(
                                  children: [
                                    ClipRRect(
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Container(
                                          height: 43.0.h,
                                          width: 43.0.w,
                                          padding: (taggedUser.profileImage == AssetConstants.pngUser ||
                                                      (taggedUser.profileImage != null &&
                                                          taggedUser.profileImage!.isEmpty) ||
                                                      taggedUser.profileImage == '') &&
                                                  taggedUser.id.toString() ==
                                                      Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                              ? EdgeInsets.all(8)
                                              : EdgeInsets.all(2),
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  color: Theme.of(context).primaryColor.withOpacity(0.2), width: 2),
                                              shape: BoxShape.circle),
                                          child: CustomImageView(
                                            onTap: () {
                                              if (taggedUser.id.toString() == currentUserId) {
                                                PersistentNavBarNavigator.pushNewScreen(context,
                                                    screen: UserProfileScreen(), withNavBar: false);
                                              } else {
                                                PersistentNavBarNavigator.pushNewScreen(context,
                                                    screen:
                                                        GetUserProfileById(userId: taggedUser.id, stackonScreen: false),
                                                    withNavBar: false);
                                              }
                                            },
                                            radius: taggedUser.id.toString() ==
                                                    Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                                ? taggedUser.profileImage?.isEmpty ?? false
                                                    ? null
                                                    : BorderRadius.circular(100.0)
                                                : BorderRadius.circular(100.0),
                                            fit: (taggedUser.profileImage == AssetConstants.pngUser ||
                                                    (taggedUser.profileImage != null &&
                                                        taggedUser.profileImage!.isEmpty) ||
                                                    taggedUser.profileImage == '')
                                                ? BoxFit.contain
                                                : BoxFit.cover,
                                            imagePath:
                                                (taggedUser.profileImage == null || taggedUser.profileImage!.isEmpty)
                                                    ? taggedUser.id.toString() ==
                                                            Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                                        ? AssetConstants.pngUser
                                                        : AssetConstants.pngUserReomve
                                                    : APIConfig.mainbaseURL + taggedUser.profileImage.toString(),
                                          ),
                                        ),
                                      ),
                                    ),
                                    buildSizedBoxW(8),
                                    Text(
                                      "@${taggedUser.username}",
                                      style: Theme.of(context).textTheme.titleLarge!.copyWith(
                                          fontSize: 15.sp, fontWeight: FontWeight.w500, color: Color(0xff292D32)),
                                    )
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        );
      } else {
        setState(() {
          istagUsershow = !istagUsershow;
        });
      }
    } catch (e) {
      Logger.lOG("Error fetching tagged users: $e");
    }
  }

// MARK: Like Comment Share
  Widget _buildLikeCommentShare() {
    return Container(
      height: 50.h,
      // width: 140.w,
      padding: EdgeInsets.symmetric(horizontal: 10.0.h),
      decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          borderRadius: BorderRadius.circular(34.r),
          boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 5)]),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildActionButton(
            context,
            onTap: () {
              setState(() {
                widget.isLiked = !widget.isLiked;
                widget.likes = widget.isLiked
                    ? (int.parse(widget.likes) + 1).toString()
                    : (int.parse(widget.likes) - 1).toString();
              });
              widget.likeonTap();
              context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
              Logger.lOG("like${widget.likes}");
            },
            iconColor: widget.isLiked ? Theme.of(context).customColors.white : Color(0xff555555),
            iconPath:
                widget.isLiked ? Assets.images.icons.homeFeed.icLike.path : Assets.images.icons.homeFeed.icUnlike.path,
            color: widget.isLiked ? Theme.of(context).primaryColor : Color(0xffF6F6F6),
            padding: widget.isLiked ? EdgeInsets.all(9.0) : null,
          ),
          buildSizedBoxW(16),
          _buildActionButton(context,
              onTap: widget.commentonTap, iconPath: Assets.images.icons.homeFeed.icComment.path),
          buildSizedBoxW(16),
          _buildActionButton(
            context,
            onTap: isMessagePermission
                ? () {
                    context.read<HomeFeedBloc>().add(UserSearchQueryChanged(''));
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      useRootNavigator: true,
                      backgroundColor: Colors.transparent,
                      builder: (context) => ShareBottomSheet(postId: widget.postId.toString(), shareType: 'fxs'),
                    );
                  }
                : () {
                    showToastNoPermission(access: 'share post');
                  },
            iconPath: Assets.images.icons.homeFeed.icShare.path,
            padding: EdgeInsets.symmetric(horizontal: 8.5, vertical: 8.0),
          ),
        ],
      ),
    );
  }

// MARK: Action Button
  Widget _buildActionButton(BuildContext context,
      {required String iconPath, Color? color, Color? iconColor, EdgeInsetsGeometry? padding, void Function()? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 37.h,
        width: 37.w,
        clipBehavior: Clip.hardEdge,
        padding: padding ?? EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: color ?? Color(0xffF6F6F6),
          shape: BoxShape.circle,
        ),
        child: CustomImageView(
          fit: BoxFit.contain,
          imagePath: iconPath,
          color: iconColor,
        ),
      ),
    );
  }

// MARK: Post Detail
  Widget _buildPostDetail(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).customColors.black.withOpacity(0.2),
              blurRadius: 2,
            )
          ],
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(20.r),
            bottomRight: Radius.circular(20.r),
          )),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildSizedBoxH(20.h),
                widget.likes == "0"
                    ? const SizedBox.shrink()
                    : Text(_getLikeText(widget.isLiked, int.parse(widget.likes)),
                        style: Theme.of(context)
                            .textTheme
                            .titleLarge!
                            .copyWith(fontSize: 12.5.sp, fontWeight: FontWeight.w700)),
                CaptionPreviewWidget(
                    caption: widget.caption,
                    comment: widget.latestcomments,
                    commentonTap: widget.commentonTap,
                    isTextPost: widget.isTextPost),
                buildSizedBoxH(4),
                Text(
                  getPostTimeAgoFromUTC(widget.postTime.toString()),
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontSize: 10.sp,
                        fontWeight: FontWeight.w400,
                      ),
                ),
              ],
            ),
          ),
          // if (widget.userId.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID))
          //   Column(
          //     children: [
          //       buildSizedBoxH(20.h),
          //       InkWell(
          //         onTap: _toggleAnalytics,
          //         child: CustomImageView(
          //           height: 30.h,
          //           width: 30.w,
          //           imagePath: _isAnalyticsVisible
          //               ? Assets.images.icons.social.icAnalytics.path
          //               : Assets.images.icons.social.icAnalyticsUnfill.path,
          //         ),
          //       ),
          //     ],
          //   )
        ],
      ),
    );
  }

  String _getLikeText(bool isLiked, int likeCount) {
    if (likeCount == 0) {
      return "";
    } else if (isLiked && likeCount == 1) {
      return "Liked by you";
    } else if (!isLiked && likeCount > 0) {
      return "Liked by ${abbreviateNumber(likeCount)} others";
    } else {
      return "Liked by you and ${abbreviateNumber(likeCount - 1)} others";
    }
  }

  void _onDoubleTap() {
    context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
    if (_controller.isAnimating) {
      _controller.stop();
    }
    _controller.reset();
    _controller.forward();
    setState(() {
      _isAnimationVisible = true;
    });
  }

  bool _hasAnyVideoInMedia() {
    return widget.postMedia.any((mediaUrl) => isVideo(mediaUrl));
  }

  _buildMoreOptionBottomSheet(BuildContext context) {
    return showModalBottomSheet(
      useRootNavigator: true,
      context: context,
      backgroundColor: Colors.transparent,
      builder: (_) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(40.0.r)),
          ),
          padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
          child: StatefulBuilder(
            builder: (context, setState) => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  height: 3.h,
                  width: 40.w,
                  decoration: BoxDecoration(
                    color: Color(0xffE9EBEA),
                    borderRadius: BorderRadius.circular(100.r),
                  ),
                ),
                buildSizedBoxH(8.h),

                // Share Option
                // _buildMoreBottomsheetItem(
                //   imagePath: Assets.images.svg.homeFeed.svgShare.path,
                //   label: Lang.of(context).lbl_share,
                //   onTap: () {
                //     Logger.lOG("Share");
                //     NavigatorService.goBack();
                //   },
                // ),
                // MARK: Duplicate Post
                if (!widget.isTextPost && !_hasAnyVideoInMedia())
                  if (isPostPermission)
                    BlocBuilder<HomeFeedBloc, HomeFeedState>(
                      builder: (context, state) {
                        return _buildMoreBottomsheetItem(
                          imagePath: Assets.images.svg.homeFeed.svgDuplicateOutline.path,
                          label: "Create Duplicate Post",
                          isPermission: isPostPermission,
                          massageLabel: "create duplicate post",
                          onTap: () async {
                            NavigatorService.goBack();
                            VibrationHelper.singleShortBuzz();

                            context.read<PostBloc>().add(
                                  AIgenerateDuplicatePostEvent(
                                      prompt: widget.caption,
                                      media: widget.state.posts[widget.index].files.first,
                                      context: context),
                                );
                            showModalBottomSheet(
                              useRootNavigator: true,
                              context: context,
                              useSafeArea: true,
                              backgroundColor: Colors.transparent,
                              isScrollControlled: true,
                              builder: (_) {
                                return Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.vertical(top: Radius.circular(40.0.r)),
                                  ),
                                  padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
                                  child: BlocBuilder<PostBloc, PostState>(
                                    builder: (context, postState) {
                                      captionController.text = postState.generateDuplicatePostModel?.result ?? "";
                                      return StatefulBuilder(
                                        builder: (context, setState) => Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Container(
                                              height: 3.h,
                                              width: 40.w,
                                              decoration: BoxDecoration(
                                                color: Color(0xffE9EBEA),
                                                borderRadius: BorderRadius.circular(100.r),
                                              ),
                                            ),
                                            buildSizedBoxH(10.h),
                                            Row(
                                              children: [
                                                InkWell(
                                                  onTap: () {
                                                    FocusScope.of(context).unfocus();
                                                    NavigatorService.goBack();
                                                  },
                                                  child: Padding(
                                                    padding: const EdgeInsets.all(8.0),
                                                    child: CustomImageView(
                                                      imagePath: Assets.images.svg.authentication.icBackArrow.path,
                                                      height: 16.h,
                                                    ),
                                                  ),
                                                ),
                                                buildSizedBoxW(20.w),
                                                Text(
                                                  "Generated Duplicate Post",
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .titleLarge
                                                      ?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
                                                ),
                                              ],
                                            ),
                                            buildSizedBoxH(14.h),
                                            Flexible(
                                              child: SingleChildScrollView(
                                                child: BlocBuilder<PostBloc, PostState>(
                                                  builder: (context, postState) {
                                                    captionController.text =
                                                        postState.generateDuplicatePostModel?.result ?? "";
                                                    return postState.isAIGeneratingDuplicatePostloading
                                                        ? buildShimmerLoading() // Shimmer loading widget
                                                        : Column(
                                                            children: [
                                                              CustomImageView(
                                                                height: 350.h,
                                                                width: double.infinity,
                                                                imagePath: postState.duplicatePostFile?.path.toString(),
                                                                fit: BoxFit.cover,
                                                                radius: BorderRadius.circular(10.r),
                                                              ),
                                                              buildSizedBoxH(28.h),
                                                              DetectableTextField(
                                                                textInputAction: TextInputAction.newline,
                                                                readOnly: true,
                                                                maxLines: 6,
                                                                minLines: 2,
                                                                maxLength: 1000,
                                                                controller: captionController,
                                                                decoration: InputDecoration(
                                                                  alignLabelWithHint: true,
                                                                  labelText: 'Description',
                                                                  floatingLabelBehavior: FloatingLabelBehavior.auto,
                                                                  border: OutlineInputBorder(
                                                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                                                    borderRadius: BorderRadius.circular(12.r),
                                                                  ),
                                                                  enabledBorder: OutlineInputBorder(
                                                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                                                    borderRadius: BorderRadius.circular(12.r),
                                                                  ),
                                                                  focusedBorder: OutlineInputBorder(
                                                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                                                    borderRadius: BorderRadius.circular(12.r),
                                                                  ),
                                                                  errorBorder: OutlineInputBorder(
                                                                    borderSide: BorderSide(
                                                                        color: Theme.of(context).colorScheme.error),
                                                                    borderRadius: BorderRadius.circular(12.r),
                                                                  ),
                                                                  focusedErrorBorder: OutlineInputBorder(
                                                                    borderSide: BorderSide(
                                                                        color: Theme.of(context).colorScheme.error),
                                                                    borderRadius: BorderRadius.circular(12.r),
                                                                  ),
                                                                  suffixIcon: SizedBox(),
                                                                ),
                                                              ),
                                                              buildSizedBoxH(20.h),
                                                              CustomElevatedButton(
                                                                width: double.infinity,
                                                                text: "Done",
                                                                onPressed: () {
                                                                  Navigator.pop(context);
                                                                  // NavigatorService.goBack();
                                                                  VibrationHelper.singleShortBuzz();

                                                                  // Generated image ane description sathe next screen ma navigate karo
                                                                  NavigatorService.pushNamed(
                                                                    AppRoutes.uploadPostScrteen,
                                                                    arguments: [
                                                                      [
                                                                        postState.duplicatePostFile?.path // image path
                                                                      ], // image list
                                                                      false, // isTextPost
                                                                      true, // isGeneratedPost
                                                                      captionController.text.trim(), // caption
                                                                    ],
                                                                  );
                                                                },
                                                              ),
                                                            ],
                                                          );
                                                  },
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            );
                          },
                        );
                      },
                    ),

                // Save Option
                if (!widget.isTextPost)
                  if (isPostPermission)
                    BlocBuilder<HomeFeedBloc, HomeFeedState>(
                      builder: (context, state) {
                        // Find the current post in the state to get the latest isSaved value
                        bool currentIsSaved = widget.isSaved;

                        // Check in posts list
                        final postsWithId = state.posts.where((p) => p.id == widget.postId);
                        if (postsWithId.isNotEmpty) {
                          currentIsSaved = postsWithId.first.isSaved;
                        } else {
                          // Check in videos list
                          final videosWithId = state.video.where((v) => v.id == widget.postId);
                          if (videosWithId.isNotEmpty) {
                            currentIsSaved = videosWithId.first.isSaved;
                          }
                        }

                        return _buildMoreBottomsheetItem(
                          imagePath: currentIsSaved
                              ? Assets.images.pngs.homeFeed.svgSaveFill.path
                              : Assets.images.pngs.homeFeed.svgSave.path,
                          label: currentIsSaved ? Lang.of(context).lbl_unsave : Lang.of(context).lbl_save,
                          isPermission: isPostPermission,
                          massageLabel: "save/unsave post",
                          onTap: () {
                            Logger.lOG(currentIsSaved ? "Unsave" : "Save");

                            NavigatorService.goBack();
                            VibrationHelper.singleShortBuzz();
                            context.read<HomeFeedBloc>().add(SavedPostSocketEvent(postId: widget.postId.toString()));
                          },
                        );
                      },
                    ),

                // Edit Option
                if (widget.userId.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID))
                  if (isPostPermission)
                    widget.isanalytics == false
                        ? _buildMoreBottomsheetItem(
                            imagePath: Assets.images.svg.homeFeed.svgEdit.path,
                            label: Lang.of(context).lbl_edit,
                            isPermission: isPostPermission,
                            massageLabel: "edit post",
                            onTap: () {
                              // NavigatorService.pushNamed(AppRoutes.EditPostscreen, arguments: [
                              //   widget.userpost == true
                              //       ? widget.state.getProfilePost[widget.index]
                              //       : widget.userByIDpost == true
                              //           ? widget.state.isuserProfileposts[widget.index]
                              //           : widget.state.posts[widget.index],
                              //   widget.userVideo == true
                              //       ? widget.state.getProfilevideo[widget.index]
                              //       : widget.userByIDvideo == true
                              //           ? widget.state.getProfileByIDvideo[widget.index]
                              //           : widget.state.video[widget.index] ?? {},
                              //   widget.index,
                              //   widget.isVideo == true || widget.userByIDvideo == true || widget.userVideo == true ? true : false,
                              // ]);
                              NavigatorService.goBack();
                              NavigatorService.pushNamed(
                                AppRoutes.EditPostscreen,
                                arguments: [
                                  widget.isTextPost && widget.userpost
                                      ? widget.state.getUserTextPostData[widget.index]
                                      : widget.userpost
                                          ? widget.state.getProfilePost[widget.index]
                                          : widget.userByIDpost
                                              ? widget.state.isuserProfileposts[widget.index]
                                              : widget.isNotificationPost ?? false
                                                  ? widget.state.getpostbyIdData[widget.index]
                                                  : widget.isDiscoverPosts ?? false
                                                      ? widget.state.isDiscoverposts[widget.index]
                                                      : widget.state.posts.isNotEmpty &&
                                                              widget.index < widget.state.posts.length
                                                          ? widget.state.posts[widget.index]
                                                          : null, // fallback

                                  widget.userVideo
                                      ? widget.state.getProfilevideo.isNotEmpty &&
                                              widget.index < widget.state.getProfilevideo.length
                                          ? widget.state.getProfilevideo[widget.index]
                                          : null
                                      : widget.userByIDvideo
                                          ? widget.state.getProfileByIDvideo.isNotEmpty &&
                                                  widget.index < widget.state.getProfileByIDvideo.length
                                              ? widget.state.getProfileByIDvideo[widget.index]
                                              : null
                                          : widget.isNotificationPost ?? false
                                              ? widget.state.getpostbyIdData[widget.index]
                                              : widget.isDiscoverPosts ?? false
                                                  ? widget.state.isDiscoverposts[widget.index]
                                                  : widget.state.video.isNotEmpty &&
                                                          widget.index < widget.state.video.length
                                                      ? widget.state.video[widget.index]
                                                      : null, // fallback

                                  widget.index,

                                  widget.isVideo || widget.userByIDvideo || widget.userVideo,
                                  widget.isTextPost,
                                  reelService,
                                ],
                              );
                              Logger.lOG("Edit");
                            },
                          )
                        : SizedBox.shrink(),

                //MARK:Bloc Option
                if (widget.userId.toString() != Prefobj.preferences?.get(Prefkeys.USER_ID))
                  if (isBlockPermission)
                    BlocBuilder<HomeFeedBloc, HomeFeedState>(
                      builder: (context, state) {
                        return _buildMoreBottomsheetItem(
                          imagePath: Assets.images.svg.setting.svgBlockedPeople.path,
                          label: Lang.of(context).lbl_block,
                          isPermission: isBlockPermission,
                          massageLabel: 'block user',
                          onTap: () {
                            NavigatorService.goBack();
                            showDialog(
                              context: context,
                              builder: (ctx) {
                                bool isLoading = false;
                                return WillPopScope(
                                  onWillPop: () async => false,
                                  child: StatefulBuilder(
                                    builder: (ctx, setState) {
                                      return CustomAlertDialog(
                                        imagePath: Assets.images.pngs.other.pngBlock.path,
                                        imageheight: 30.h,
                                        imagewidth: 30.w,
                                        title: "Block User?",
                                        subtitle: Lang.of(ctx).msg_block_user_title,
                                        onConfirmButtonPressed: () async {
                                          setState(() {
                                            isLoading = true;
                                          });
                                          ctx.read<HomeFeedBloc>().add(BlockUserApiEvent(
                                              userId: widget.userId.toString(), context: ctx, isblocked: false));
                                          VibrationHelper.singleShortBuzz();
                                        },
                                        confirmButtonText: Lang.of(ctx).lbl_confirm,
                                        isLoading: isLoading,
                                      );
                                    },
                                  ),
                                );
                              },
                            );
                          },
                        );
                      },
                    ),

                // Delete Option
                if (widget.userId.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID))
                  if (isPostPermission)
                    _buildMoreBottomsheetItem(
                      imagePath: Assets.images.svg.setting.svgDeleteAccount.path,
                      label: Lang.of(context).lbl_delete,
                      isPermission: isPostPermission,
                      massageLabel: 'delete post',
                      onTap: () {
                        Logger.lOG("Delete");
                        NavigatorService.goBack();
                        _buildDeletePostAlert(context, widget.state, widget.isNotificationPost).then((value) {
                          if (widget.isNotificationPost == true) {
                            context.read<NotificationBloc>().add(const GetNotificationEvent(page: 1));
                            VibrationHelper.singleShortBuzz();
                            NavigatorService.goBack();
                          }
                        });
                      },
                    ),
              ],
            ),
          ),
        );
      },
      isScrollControlled: true,
    );
  }

  Widget _buildMoreBottomsheetItem({
    required String imagePath,
    required String label,
    required VoidCallback onTap,
    required bool isPermission,
    required String massageLabel,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 6.h),
      decoration: BoxDecoration(
        color: isPermission
            ? Theme.of(context).primaryColor.withOpacity(0.2)
            : Theme.of(context).primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(30.r),
      ),
      child: ListTile(
        minTileHeight: 60.h,
        leading: CustomImageView(
          height: 20.h,
          width: 20.w,
          imagePath: imagePath,
          fit: BoxFit.contain,
          color: !isPermission ? Colors.black45 : null,
        ),
        title: Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontSize: 16.sp,
                color: !isPermission ? Colors.black45 : null,
              ),
        ),
        onTap: isPermission
            ? onTap
            : () {
                NavigatorService.goBack();
                showToastNoPermission(access: massageLabel);
              },
      ),
    );
  }

  _buildDeletePostAlert(BuildContext ctx, HomeFeedState state, bool? isNotificationPost) {
    return showDialog(
      barrierDismissible: state.isDeleteLoading,
      context: ctx,
      builder: (ctx) {
        bool isLoading = false;

        return WillPopScope(
          onWillPop: () async => false,
          child: StatefulBuilder(
            builder: (ctx, setState) {
              return CustomAlertDialog(
                imageheight: 70.h,
                imagewidth: 70.w,
                imagePath: Assets.images.pngs.other.pngDeletePost.path,
                title: Lang.of(context).lbl_delete_post_title,
                subtitle: '',
                onConfirmButtonPressed: () async {
                  setState(() {
                    isLoading = true;
                  });
                  // if (widget.screenType == Lang.of(context).lbl_user_profile) {
                  //   context.read<UserProfileBloc>().add(ProfileDeletePostApiEvent(
                  //         postId: widget.postId,
                  //         index: widget.index,
                  //       ));
                  // } else if (widget.screenType ==
                  //     Lang.of(context).lbl_user_profile_id) {
                  //   context
                  //       .read<UserProfileIdBloc>()
                  //       .add(ProfileDeletePostIdApiEvent(
                  //         postId: widget.postId,
                  //         index: widget.index,
                  //       ));
                  // } else {
                  context.read<HomeFeedBloc>().add(DeletePostApiEvent(
                      postId: widget.postId,
                      index: widget.index,
                      userByIDpost: widget.userByIDpost,
                      userpost: widget.userpost,
                      isVideo: widget.isVideo,
                      isPost: widget.isPost,
                      userByIDvideo: widget.userByIDvideo,
                      userVideo: widget.userVideo,
                      context: context,
                      isTextPost: widget.isTextPost,
                      isprofilrpostDelete: widget.isprofilrpostDelete ?? false,
                      isNotificationPost: widget.isNotificationPost));

                  //}
                },
                confirmButtonText: Lang.of(ctx).lbl_delete,
                isLoading: isLoading,
              );
            },
          ),
        );
      },
    );
  }

  Widget buildExpandedAnalyticsList(
    BuildContext context,
    AnalyticsModel? analyticsModel,
  ) {
    List<Widget> platformCards = [];
    if (analyticsModel?.data != null) {
      AnalyticsData data = analyticsModel!.data!;

      platformCards.add(_buildAnalyticsContainer(
        context,
        "Flowkar",
        data.likes ?? 0,
        data.comments ?? 0,
        Assets.images.pngs.flowkar.path,
      ));

      if (data.instagram != null) {
        platformCards.add(_buildAnalyticsContainer(
          context,
          "Instagram",
          data.instagram?.like ?? 0,
          data.instagram?.comments ?? 0,
          Assets.images.icons.social.insta.path,
        ));
      }

      if (data.linkedin != null) {
        platformCards.add(_buildAnalyticsContainer(
          context,
          "Linkedin",
          data.linkedin?.like ?? 0,
          data.linkedin?.comments ?? 0,
          Assets.images.icons.social.icLinkdin.path,
        ));
      }

      if (data.pinterest != null) {
        platformCards.add(_buildAnalyticsContainer(
          context,
          "pinterest",
          data.pinterest?.like ?? 0,
          data.pinterest?.comments ?? 0,
          Assets.images.icons.social.icPintrest.path,
        ));
      }

      if (data.vimeo != null) {
        platformCards.add(_buildAnalyticsContainer(
          context,
          "Vimeo",
          data.vimeo?.like ?? 0,
          data.vimeo?.comments ?? 0,
          Assets.images.icons.social.v.path,
        ));
      }
      if (data.tumblr != null) {
        platformCards.add(_buildAnalyticsContainer(
          context,
          "tumblr",
          data.tumblr?.like ?? 0,
          data.tumblr?.comments ?? 0,
          Assets.images.icons.social.icTumblr.path,
        ));
      }
      if (data.reddit != null) {
        platformCards.add(_buildAnalyticsContainer(
          context,
          "reddit",
          data.reddit?.like ?? 0,
          data.reddit?.comments ?? 0,
          Assets.images.icons.social.icReddit.path,
        ));
      }
      if (data.thread != null) {
        platformCards.add(_buildAnalyticsContainer(
          context,
          "Thread",
          data.thread?.like ?? 0,
          data.thread?.comments ?? 0,
          Assets.images.icons.social.icThread.path,
        ));
      }
      if (data.facebook != null) {
        platformCards.add(_buildAnalyticsContainer(
          context,
          "facebook",
          data.facebook?.like ?? 0,
          data.facebook?.comments ?? 0,
          Assets.images.icons.social.facebook.path,
        ));
      }
      if (data.youtube != null) {
        platformCards.add(_buildAnalyticsContainer(
          context,
          "youtube",
          data.youtube?.like ?? 0,
          data.youtube?.comments ?? 0,
          Assets.images.icons.social.youtube.path,
        ));
      }
      if (data.tiktok != null) {
        platformCards.add(_buildAnalyticsContainer(
          context,
          "tiktok",
          data.tiktok?.like ?? 0,
          data.tiktok?.comments ?? 0,
          Assets.images.icons.social.tiktok.path,
        ));
      }
    }

    return widget.state.anyliticsLoading
        ? Padding(
            padding: EdgeInsets.only(bottom: 10.h),
            child: AnalyticsCardShimmer(),
          )
        : Column(
            // mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 16.w, right: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      textAlign: TextAlign.left,
                      "Connected Social Platforms",
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium
                          ?.copyWith(fontWeight: FontWeight.w700, fontSize: 15.sp),
                    ),
                    if (widget.isanalytics == true)
                      GestureDetector(
                        onTap: () {
                          // if (platformCards.length > 2) {
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.vertical(
                                top: Radius.circular(40.0.r),
                              ),
                            ),
                            builder: (context) {
                              return FractionallySizedBox(
                                heightFactor: 0.9,
                                child: Column(
                                  children: [
                                    buildSizedBoxH(16.0),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        buildSizedBoxW(0),
                                        Container(
                                          height: 4.h,
                                          width: 60.w,
                                          decoration: BoxDecoration(
                                              color: Colors.grey.shade200, borderRadius: BorderRadius.circular(10.r)),
                                        ),
                                        buildSizedBoxW(0),
                                      ],
                                    ),
                                    buildSizedBoxH(16.0),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0.r)),
                                      ),
                                      child: AnimationLimiter(
                                        child: GridView.builder(
                                          physics: const ScrollPhysics(),
                                          padding: EdgeInsets.symmetric(horizontal: 14.w),
                                          shrinkWrap: true,
                                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: 2,
                                            childAspectRatio: 1,
                                            crossAxisSpacing: 14,
                                            mainAxisSpacing: 10,
                                          ),
                                          itemCount: platformCards.length,
                                          itemBuilder: (context, index) {
                                            return AnimationConfiguration.staggeredGrid(
                                              position: index,
                                              duration: const Duration(milliseconds: 800),
                                              columnCount: 2,
                                              child: SlideAnimation(
                                                verticalOffset: 100.0,
                                                child: FadeInAnimation(child: platformCards[index]),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          );
                          // }
                        },
                        child: Text(
                          textAlign: TextAlign.right,
                          "See all",
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w700, fontSize: 15.sp, color: Color(0xffC4C4C4)),
                        ),
                      ),
                  ],
                ),
              ),
              buildSizedBoxH(10),
              SizedBox(
                height: 186.h,
                child: ListView.builder(
                  padding: EdgeInsets.only(right: 10.w),
                  scrollDirection: Axis.horizontal,
                  itemCount: platformCards.length,
                  itemBuilder: (context, index) {
                    return platformCards[index];
                  },
                ),
              ),
              buildSizedBoxH(16),
            ],
          );
  }

  Widget _buildAnalyticsContainer(
    BuildContext context,
    String platformName,
    int likes,
    int comments,
    String fileUrl,
  ) {
    return Container(
      height: 186.h,
      width: 170.w,
      margin: EdgeInsets.only(left: 14.w),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.socialContainer,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 18.w, top: 18.h, bottom: 10.h),
            width: 51.w,
            height: 51.h,
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: Colors.white,
            ),
            child: CustomImageView(imagePath: fileUrl),
          ),
          _buildSocialConnectTitle(context, platformName),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Container(
                margin: EdgeInsets.only(top: 12.h),
                width: 51.w,
                height: 51.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.r),
                  color: Colors.white,
                ),
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        CustomImageView(
                          margin: EdgeInsets.only(top: 2.h),
                          // width: 18.w,
                          height: 17.h,
                          alignment: Alignment.center,
                          imagePath: Assets.images.icons.homeFeed.icLikeFill.path,
                          fit: BoxFit.fill,
                        ),
                        Text(
                          "Likes",
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w400, fontSize: 8.sp, color: Theme.of(context).primaryColor),
                        ),
                      ],
                    ),
                    Positioned(
                      top: -5.h,
                      right: -5.h,
                      child: CircleAvatar(
                        radius: 9.r,
                        backgroundColor: Color(0xffD9D9D9),
                        child: Text(
                          likes.toString(),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.bold, fontSize: 10.sp, color: Theme.of(context).primaryColor),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              //Comment
              Container(
                margin: EdgeInsets.only(top: 12.h),
                width: 51.w,
                height: 51.h,
                // padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.r),
                  color: Colors.white,
                ),
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        CustomImageView(
                          margin: EdgeInsets.only(top: 2.h),
                          // width: 18.w,
                          height: 18.h,
                          alignment: Alignment.center,
                          imagePath: Assets.images.icons.social.icComment.path,
                          fit: BoxFit.fill,
                        ),
                        Text(
                          "Comments",
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w400, fontSize: 8.sp, color: Theme.of(context).primaryColor),
                        ),
                      ],
                    ),
                    Positioned(
                        top: -5.h,
                        right: -5.h,
                        child: CircleAvatar(
                            radius: 9.r,
                            backgroundColor: Color(0xffD9D9D9),
                            child: Text(
                              comments.toString(),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  fontWeight: FontWeight.bold, fontSize: 10.sp, color: Theme.of(context).primaryColor),
                            )))
                  ],
                ),
              ),
            ],
          ),
          buildSizedBoxH(12),
        ],
      ),
    );
  }

  Widget _buildSocialConnectTitle(BuildContext context, String? text) {
    return Padding(
      padding: EdgeInsets.only(left: 20.w),
      child: Text(
        text!,
        textAlign: TextAlign.center,
        style: Theme.of(context)
            .textTheme
            .titleLarge
            ?.copyWith(fontWeight: FontWeight.w700, fontSize: 14.sp, color: Colors.black),
      ),
    );
  }
}

class MyBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(BuildContext context, Widget child, ScrollableDetails details) {
    return child;
  }
}

class TaggedUser {
  final String username;
  final int id;
  final String? profileImage;

  TaggedUser({required this.username, required this.id, this.profileImage});
}

String getPostTimeAgoFromUTC(String utcString) {
  try {
    final utcDateTime = DateTime.parse(utcString).toUtc();
    final localDateTime = utcDateTime.toLocal();
    final now = DateTime.now();
    final difference = now.difference(localDateTime);

    if (difference.isNegative || difference.inSeconds < 5) return "Just now";

    final bool isToday =
        localDateTime.year == now.year && localDateTime.month == now.month && localDateTime.day == now.day;

    final bool isYesterday =
        localDateTime.year == now.year && localDateTime.month == now.month && localDateTime.day == now.day - 1;

    if (isToday) {
      if (difference.inSeconds < 60) {
        return difference.inSeconds == 1 ? "a second ago" : "${difference.inSeconds} seconds ago";
      }
      if (difference.inMinutes < 60) {
        return difference.inMinutes == 1 ? "a minute ago" : "${difference.inMinutes} minutes ago";
      }
      if (difference.inHours < 24) {
        return difference.inHours == 1 ? "an hour ago" : "${difference.inHours} hours ago";
      }
    }

    if (isYesterday) return "Yesterday";

    if (difference.inDays < 7) {
      return difference.inDays == 1 ? "a day ago" : "${difference.inDays} days ago";
    }

    final formattedDate = intl.DateFormat('d MMM, yyyy').format(localDateTime);
    return formattedDate;
  } catch (e) {
    return "Invalid time";
  }
}
